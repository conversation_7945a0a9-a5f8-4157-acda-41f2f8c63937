# Development Guidelines

## Overview
This document outlines the development guidelines and best practices for the Meta-Agent codebase after the comprehensive refactoring.

## Code Organization Principles

### 1. Single Responsibility Principle
- Each module should have one clear, well-defined responsibility
- Functions should do one thing and do it well
- Components should focus on a single UI concern

### 2. Separation of Concerns
- Business logic separated from UI components
- API calls separated from component rendering
- State management separated from presentation logic

### 3. Modularity
- Code organized into focused, reusable modules
- Clear interfaces between modules
- Easy to test components in isolation

## File Structure Guidelines

### Backend Structure
```
backend/app/
├── api/v1/endpoints/
│   ├── agent_creation.py      # Agent creation logic
│   ├── agent_crud.py          # Basic CRUD operations
│   ├── agent_execution.py     # Agent execution logic
│   ├── agent_management.py    # Management operations
│   ├── agents.py              # Main router
│   ├── auth_registration.py   # User registration
│   ├── auth_login.py          # Login logic
│   ├── auth_2fa.py            # 2FA operations
│   ├── auth_profile.py        # Profile management
│   ├── auth_password.py       # Password operations
│   ├── auth_session.py        # Session management
│   ├── auth.py                # Main auth router
│   └── ...
├── services/
│   ├── agent_runtime.py       # Agent execution runtime
│   ├── agent_loader.py        # Agent loading utilities
│   ├── dynamic_loader.py      # Main orchestrator
│   └── ...
└── ...
```

### Frontend Structure
```
frontend/src/
├── lib/
│   ├── api/
│   │   ├── base.ts            # Base API client
│   │   ├── agents.ts          # Agent APIs
│   │   ├── templates.ts       # Template APIs
│   │   ├── testing.ts         # Testing APIs
│   │   ├── auth.ts            # Auth APIs
│   │   ├── misc.ts            # Misc APIs
│   │   └── index.ts           # Main aggregator
│   └── api.ts                 # Legacy compatibility
├── components/features/
│   ├── agent-testing/
│   │   ├── hooks/
│   │   │   ├── useTestExecution.ts
│   │   │   ├── useVariableTracking.ts
│   │   │   └── useTestHistory.ts
│   │   ├── components/
│   │   │   ├── TestConfiguration.tsx
│   │   │   ├── TestResults.tsx
│   │   │   ├── VariableTrackingPanel.tsx
│   │   │   └── TestHistoryPanel.tsx
│   │   └── test-interface.tsx
│   ├── logs/
│   │   ├── LogsStatistics.tsx
│   │   ├── LogsList.tsx
│   │   └── LogDetailDialog.tsx
│   └── ...
└── ...
```

## Naming Conventions

### Files and Directories
- Use kebab-case for directories: `agent-testing`, `test-history`
- Use PascalCase for React components: `TestConfiguration.tsx`
- Use camelCase for hooks: `useTestExecution.ts`
- Use snake_case for Python files: `agent_creation.py`

### Functions and Variables
- Use camelCase for JavaScript/TypeScript: `handleSubmit`, `isLoading`
- Use snake_case for Python: `create_agent`, `user_id`
- Use descriptive names that explain the purpose

### Components and Classes
- Use PascalCase: `TestConfiguration`, `AgentAPIClient`
- Use descriptive names that indicate the component's purpose

## Module Guidelines

### Creating New Modules

1. **Identify Responsibility**: Clearly define what the module should do
2. **Define Interface**: Create clear input/output contracts
3. **Keep It Small**: Aim for modules under 300 lines
4. **Single Purpose**: Each module should have one main responsibility
5. **Test Early**: Write tests as you develop the module

### API Module Guidelines

```typescript
// Good: Focused API client
export class AgentAPIClient extends BaseAPIClient {
  async getAgents(filters?: AgentFilters): Promise<ApiResponse<Agent[]>> {
    return this.get<Agent[]>("/api/v1/agents", filters);
  }
  
  async createAgent(request: CreateAgentRequest): Promise<ApiResponse<Agent>> {
    return this.post<Agent>("/api/v1/agents", request);
  }
}

// Bad: Mixed responsibilities
export class APIClient {
  async getAgents() { /* ... */ }
  async getUsers() { /* ... */ }
  async uploadFile() { /* ... */ }
  async sendEmail() { /* ... */ }
}
```

### Component Guidelines

```typescript
// Good: Focused component with clear props
interface TestConfigurationProps {
  input: string;
  onInputChange: (value: string) => void;
  isLoading: boolean;
  onSubmit: () => void;
}

export function TestConfiguration({ input, onInputChange, isLoading, onSubmit }: TestConfigurationProps) {
  // Component logic focused on test configuration
}

// Bad: Component doing too many things
export function TestInterface() {
  // Handles configuration, execution, results, history, etc.
}
```

### Hook Guidelines

```typescript
// Good: Focused hook with clear responsibility
export function useTestExecution({ agent, onProgressUpdate }: UseTestExecutionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<string | null>(null);
  
  const executeTest = useCallback(async (input: string) => {
    // Test execution logic
  }, []);
  
  return {
    isLoading,
    response,
    executeTest
  };
}
```

## Error Handling

### Backend Error Handling
```python
# Use consistent error handling patterns
try:
    result = await some_operation()
    return {"success": True, "data": result}
except ValueError as e:
    logger.warning(f"Validation error: {str(e)}")
    raise HTTPException(status_code=400, detail=str(e))
except Exception as e:
    logger.error(f"Unexpected error: {str(e)}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

### Frontend Error Handling
```typescript
// Use consistent error handling in API calls
try {
  const response = await api.agents.getAgents();
  if (response.success) {
    setAgents(response.data);
  } else {
    setError(response.error?.message || "Failed to load agents");
  }
} catch (error) {
  setError("Network error occurred");
}
```

## Testing Guidelines

### Backend Testing
- Write unit tests for each module
- Test error conditions and edge cases
- Use pytest fixtures for common test data
- Aim for 80%+ code coverage

### Frontend Testing
- Test hooks in isolation using React Testing Library
- Test components with different prop combinations
- Mock API calls in tests
- Test user interactions and state changes

## Performance Guidelines

### Backend Performance
- Use async/await for I/O operations
- Implement proper database indexing
- Use pagination for large data sets
- Cache frequently accessed data

### Frontend Performance
- Use React.memo for expensive components
- Implement proper loading states
- Use useCallback and useMemo appropriately
- Lazy load components when possible

## Documentation Guidelines

### Code Documentation
- Write clear docstrings for Python functions
- Use JSDoc comments for TypeScript functions
- Document complex business logic
- Keep comments up to date with code changes

### API Documentation
- Document all endpoints with examples
- Include request/response schemas
- Document error conditions
- Provide usage examples

## Migration Guidelines

### Adding New Features
1. Create focused modules following the established patterns
2. Write tests before implementing functionality
3. Update documentation
4. Ensure backward compatibility

### Modifying Existing Code
1. Identify the appropriate module for changes
2. Keep changes focused and minimal
3. Update tests to reflect changes
4. Verify no breaking changes

## Best Practices Summary

1. **Keep modules small and focused** (< 300 lines)
2. **Use clear, descriptive names** for files, functions, and variables
3. **Separate concerns** - don't mix UI, business logic, and data access
4. **Write tests** for all new functionality
5. **Handle errors consistently** across the application
6. **Document your code** and keep documentation current
7. **Follow established patterns** when adding new features
8. **Test thoroughly** before deploying changes

This refactored codebase provides a solid foundation for future development while maintaining high code quality and developer productivity.
