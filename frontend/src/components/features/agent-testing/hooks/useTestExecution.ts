/**
 * Hook for managing test execution state and logic
 */

import { useState, useCallback } from 'react';
import { api } from '@/lib/api';
import { type AIModelOverride as AIModelOverrideType } from '../ai-model-override';

export interface ExecutionState {
  isRunning: boolean;
  currentStage: string;
  currentStep: string;
  completedSteps: string[];
  hasResults: boolean;
}

export interface ProgressState {
  stage: string;
  message: string;
  progress: number;
  currentStep: number;
  totalSteps: number;
  stepName: string;
  assignee: string;
}

export interface UseTestExecutionProps {
  agent: any;
  onProgressUpdate?: (progress: any) => void;
  onVariableUpdate?: (variables: any[]) => void;
}

export function useTestExecution({ agent, onProgressUpdate, onVariableUpdate }: UseTestExecutionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<string | null>(null);
  const [responseMetadata, setResponseMetadata] = useState<any>(null);
  const [realtimeResponse, setRealtimeResponse] = useState<string>("");
  const [showProgress, setShowProgress] = useState(false);
  
  const [progress, setProgress] = useState<ProgressState>({
    stage: "",
    message: "",
    progress: 0,
    currentStep: 0,
    totalSteps: 0,
    stepName: "",
    assignee: ""
  });

  const [executionState, setExecutionState] = useState<ExecutionState>({
    isRunning: false,
    currentStage: '',
    currentStep: '',
    completedSteps: [],
    hasResults: false
  });

  const [progressUpdates, setProgressUpdates] = useState<any[]>([]);
  const [executionStages, setExecutionStages] = useState<any[]>([]);

  const resetExecution = useCallback(() => {
    setResponse(null);
    setRealtimeResponse("");
    setResponseMetadata(null);
    setShowProgress(false);
    setProgressUpdates([]);
    setExecutionStages([]);
    setExecutionState({
      isRunning: false,
      currentStage: '',
      currentStep: '',
      completedSteps: [],
      hasResults: false
    });
  }, []);

  const updateProgress = useCallback((progressData: any) => {
    console.log("🔄 [PROGRESS] Updating progress:", progressData);
    
    const newProgress = {
      stage: progressData.stage || progressData.currentStage || "",
      message: progressData.message || "",
      progress: progressData.progress || 0,
      currentStep: progressData.step || progressData.currentStep || 0,
      totalSteps: progressData.total_steps || progressData.totalSteps || 0,
      stepName: progressData.step_name || progressData.stepName || "",
      assignee: progressData.assignee || ""
    };

    setProgress(newProgress);
    
    // Update execution state
    setExecutionState(prev => ({
      ...prev,
      isRunning: true,
      currentStage: newProgress.stage,
      currentStep: newProgress.stepName,
      completedSteps: progressData.status === 'completed' 
        ? [...prev.completedSteps, newProgress.stepName].filter((step, index, arr) => arr.indexOf(step) === index)
        : prev.completedSteps
    }));

    // Add to progress updates
    setProgressUpdates(prev => [...prev, {
      ...progressData,
      timestamp: new Date().toISOString()
    }]);

    // Call external progress handler
    if (onProgressUpdate) {
      onProgressUpdate(progressData);
    }
  }, [onProgressUpdate]);

  const executeTest = useCallback(async (
    input: string,
    options: any,
    aiOverride?: AIModelOverrideType,
    apiKeys?: any[]
  ) => {
    if (!input.trim() || isLoading) {
      return;
    }

    setIsLoading(true);
    resetExecution();
    setShowProgress(true);

    // Initialize execution state
    setExecutionState({
      isRunning: true,
      currentStage: 'initializing',
      currentStep: '',
      completedSteps: [],
      hasResults: false
    });

    try {
      // Prepare request data
      const requestData = {
        input: input.trim(),
        options: options || {}
      };

      // Prepare AI configuration override
      let comprehensiveConfig = null;
      let apiKeyName = null;

      if (aiOverride?.enabled) {
        // Validate AI override configuration
        if (!aiOverride.model?.trim()) {
          throw new Error("请选择AI模型");
        }

        // Find API key name if specified
        if (aiOverride.apiKeyId && aiOverride.apiKeyId !== "default" && apiKeys) {
          const selectedKey = apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId));
          if (selectedKey) {
            apiKeyName = selectedKey.name;
          }
        }

        comprehensiveConfig = {
          provider: aiOverride.provider,
          model: aiOverride.model,
          temperature: aiOverride.temperature,
          max_tokens: aiOverride.maxTokens,
          base_url: aiOverride.baseUrl || undefined,
          custom_provider_name: aiOverride.customProviderName || undefined
        };
      }

      // Start test execution
      const startResponse = await api.testExecution.start({
        agent_id: agent.agent_id,
        input_text: requestData.input || "",
        ai_config_override: comprehensiveConfig,
        api_key_id: aiOverride?.apiKeyId && aiOverride.apiKeyId !== "default" ? Number(aiOverride.apiKeyId) : null,
        api_key_name: apiKeyName,
        input_metadata: {
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          execution_method: options.stream ? "streaming" : "sync",
          stream_mode: options.stream
        }
      });

      if (!startResponse.success || !startResponse.data) {
        throw new Error("Failed to start test execution");
      }

      const testId = startResponse.data.test_id;
      console.log("Test execution started successfully with ID:", testId);

      // Handle streaming or polling based on options
      if (options.stream) {
        await handleStreamingExecution(testId);
      } else {
        await handlePollingExecution(testId);
      }

    } catch (error) {
      console.error("Test execution failed:", error);
      setExecutionState(prev => ({
        ...prev,
        isRunning: false,
        hasResults: false
      }));
      
      // Set error response
      setResponse(`执行失败: ${error instanceof Error ? error.message : "未知错误"}`);
      setResponseMetadata({
        error: true,
        message: error instanceof Error ? error.message : "未知错误",
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
      setShowProgress(false);
    }
  }, [agent, isLoading, resetExecution, onProgressUpdate]);

  const handleStreamingExecution = useCallback(async (testId: string) => {
    try {
      // Get streaming response
      const streamResponse = await api.testExecution.stream(testId);
      
      if (streamResponse.success && streamResponse.data) {
        const { output, metadata, progress_updates } = streamResponse.data;
        
        setResponse(output || "");
        setResponseMetadata(metadata || {});
        
        if (progress_updates && Array.isArray(progress_updates)) {
          progress_updates.forEach(updateProgress);
        }
        
        setExecutionState(prev => ({
          ...prev,
          isRunning: false,
          hasResults: true
        }));
      }
    } catch (error) {
      console.error("Streaming execution failed:", error);
      throw error;
    }
  }, [updateProgress]);

  const handlePollingExecution = useCallback(async (testId: string) => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    const pollStatus = async (): Promise<void> => {
      try {
        const statusResponse = await api.testExecution.status(testId);
        
        if (statusResponse.success && statusResponse.data) {
          const { status, output, metadata, progress_updates } = statusResponse.data;
          
          // Update progress if available
          if (progress_updates && Array.isArray(progress_updates)) {
            progress_updates.forEach(updateProgress);
          }
          
          if (status === 'completed') {
            setResponse(output || "");
            setResponseMetadata(metadata || {});
            setExecutionState(prev => ({
              ...prev,
              isRunning: false,
              hasResults: true
            }));
            return;
          } else if (status === 'failed') {
            throw new Error(metadata?.error || "Execution failed");
          } else if (status === 'running' && attempts < maxAttempts) {
            attempts++;
            setTimeout(pollStatus, 5000); // Poll every 5 seconds
            return;
          } else if (attempts >= maxAttempts) {
            throw new Error("Execution timeout");
          }
        }
      } catch (error) {
        console.error("Polling failed:", error);
        throw error;
      }
    };

    await pollStatus();
  }, [updateProgress]);

  const stopExecution = useCallback(() => {
    setIsLoading(false);
    setShowProgress(false);
    setExecutionState(prev => ({
      ...prev,
      isRunning: false
    }));
  }, []);

  return {
    // State
    isLoading,
    response,
    responseMetadata,
    realtimeResponse,
    showProgress,
    progress,
    executionState,
    progressUpdates,
    executionStages,
    
    // Actions
    executeTest,
    resetExecution,
    stopExecution,
    updateProgress,
    
    // Setters for external control
    setRealtimeResponse,
    setExecutionStages
  };
}
