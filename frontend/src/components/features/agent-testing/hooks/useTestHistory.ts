/**
 * Hook for managing test history and related functionality
 */

import { useState, useCallback, useEffect } from 'react';
import { api } from '@/lib/api';

export interface TestHistoryItem {
  id: string;
  agent_id: string;
  input_text: string;
  output: string;
  status: 'completed' | 'failed' | 'running';
  created_at: string;
  execution_duration_ms?: number;
  ai_config?: any;
  metadata?: any;
  variables?: any[];
}

export interface UseTestHistoryProps {
  agent: any;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useTestHistory({ agent, autoRefresh = false, refreshInterval = 30000 }: UseTestHistoryProps) {
  const [testHistory, setTestHistory] = useState<TestHistoryItem[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [selectedTest, setSelectedTest] = useState<TestHistoryItem | null>(null);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [historyError, setHistoryError] = useState<string | null>(null);

  // Load test history for the current agent
  const loadTestHistory = useCallback(async (agentId?: string) => {
    const targetAgentId = agentId || agent?.agent_id;
    if (!targetAgentId) return;

    setLoadingHistory(true);
    setHistoryError(null);

    try {
      console.log("Loading test history for agent:", targetAgentId);
      const response = await api.testHistory.list({
        agent_id: targetAgentId,
        limit: 50,
        offset: 0
      });

      if (response.success && response.data) {
        const history = Array.isArray(response.data) ? response.data : response.data.tests || [];
        console.log("Test history loaded:", history.length, "tests");
        setTestHistory(history);
      } else {
        console.warn("Failed to load test history:", response);
        setTestHistory([]);
      }
    } catch (error) {
      console.error("Failed to load test history:", error);
      setHistoryError(error instanceof Error ? error.message : "Failed to load test history");
      setTestHistory([]);
    } finally {
      setLoadingHistory(false);
    }
  }, [agent?.agent_id]);

  // Load detailed test information
  const loadTestDetail = useCallback(async (testId: string) => {
    try {
      console.log("Loading test detail for:", testId);
      const response = await api.testHistory.getDetail(testId);
      console.log("Test detail response:", response);

      const detail = response.data || response;
      console.log("Processed detail:", detail);

      setSelectedTest(detail);
      setShowDetailDialog(true);
    } catch (error) {
      console.error("Failed to load test detail:", error);
      alert(`获取测试详情失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, []);

  // Rerun a test from history
  const rerunTest = useCallback(async (test: TestHistoryItem, onTestSubmit?: (input: string, options: any, aiOverride?: any) => void) => {
    if (!onTestSubmit) {
      console.warn("No onTestSubmit callback provided for rerun");
      return;
    }

    try {
      // Extract configuration from the test
      const aiOverride = test.ai_config ? {
        enabled: true,
        provider: test.ai_config.provider || 'openai',
        model: test.ai_config.model || '',
        temperature: test.ai_config.temperature || 0.7,
        maxTokens: test.ai_config.max_tokens || 2000,
        baseUrl: test.ai_config.base_url,
        customProviderName: test.ai_config.custom_provider_name,
        apiKeyId: test.ai_config.api_key_id
      } : undefined;

      const options = {
        stream: true // Default to streaming
      };

      console.log("Rerunning test with:", {
        input: test.input_text,
        aiOverride,
        options
      });

      // Call the test submission handler
      onTestSubmit(test.input_text, options, aiOverride, test.metadata);
    } catch (error) {
      console.error("Failed to rerun test:", error);
      alert(`重新运行测试失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, []);

  // Delete a test from history
  const deleteTest = useCallback(async (testId: string) => {
    try {
      const response = await api.testHistory.delete(testId);
      
      if (response.success) {
        // Remove from local state
        setTestHistory(prev => prev.filter(test => test.id !== testId));
        console.log("Test deleted successfully:", testId);
      } else {
        throw new Error("Failed to delete test");
      }
    } catch (error) {
      console.error("Failed to delete test:", error);
      alert(`删除测试失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, []);

  // Export test history
  const exportHistory = useCallback(async (format: 'json' | 'csv' = 'json') => {
    try {
      const response = await api.testHistory.export({
        agent_id: agent?.agent_id,
        format
      });

      if (response.success && response.data) {
        // Create download link
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: format === 'json' ? 'application/json' : 'text/csv'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `test-history-${agent?.agent_id}-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log("Test history exported successfully");
      } else {
        throw new Error("Failed to export test history");
      }
    } catch (error) {
      console.error("Failed to export test history:", error);
      alert(`导出测试历史失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, [agent?.agent_id]);

  // Clear all test history for the agent
  const clearHistory = useCallback(async () => {
    if (!agent?.agent_id) return;

    const confirmed = window.confirm("确定要清空所有测试历史吗？此操作不可撤销。");
    if (!confirmed) return;

    try {
      const response = await api.testHistory.clear(agent.agent_id);
      
      if (response.success) {
        setTestHistory([]);
        console.log("Test history cleared successfully");
      } else {
        throw new Error("Failed to clear test history");
      }
    } catch (error) {
      console.error("Failed to clear test history:", error);
      alert(`清空测试历史失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }, [agent?.agent_id]);

  // Get test statistics
  const getTestStatistics = useCallback(() => {
    const total = testHistory.length;
    const completed = testHistory.filter(test => test.status === 'completed').length;
    const failed = testHistory.filter(test => test.status === 'failed').length;
    const running = testHistory.filter(test => test.status === 'running').length;
    
    const avgDuration = testHistory
      .filter(test => test.execution_duration_ms)
      .reduce((sum, test) => sum + (test.execution_duration_ms || 0), 0) / 
      Math.max(1, testHistory.filter(test => test.execution_duration_ms).length);

    return {
      total,
      completed,
      failed,
      running,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      avgDuration: Math.round(avgDuration)
    };
  }, [testHistory]);

  // Close detail dialog
  const closeDetailDialog = useCallback(() => {
    setShowDetailDialog(false);
    setSelectedTest(null);
  }, []);

  // Auto-load history when agent changes
  useEffect(() => {
    if (agent?.agent_id) {
      loadTestHistory(agent.agent_id);
    }
  }, [agent?.agent_id, loadTestHistory]);

  // Auto-refresh history if enabled
  useEffect(() => {
    if (!autoRefresh || !agent?.agent_id) return;

    const interval = setInterval(() => {
      loadTestHistory(agent.agent_id);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, agent?.agent_id, loadTestHistory]);

  return {
    // State
    testHistory,
    loadingHistory,
    selectedTest,
    showDetailDialog,
    historyError,
    
    // Actions
    loadTestHistory,
    loadTestDetail,
    rerunTest,
    deleteTest,
    exportHistory,
    clearHistory,
    closeDetailDialog,
    
    // Computed
    testStatistics: getTestStatistics(),
    
    // Setters for external control
    setTestHistory,
    setSelectedTest,
    setShowDetailDialog
  };
}
