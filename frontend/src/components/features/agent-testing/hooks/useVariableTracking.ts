/**
 * Hook for managing variable tracking and inter-agent communication
 */

import { useState, useCallback, useEffect } from 'react';
import { api } from '@/lib/api';
import { createVariableTrackingWebSocket, type VariableUpdate, type VariableTrackingCallbacks } from '@/lib/websocket';

export interface VariablePlaceholder {
  id: string;
  placeholderName: string;
  sourceStep: string;
  sourceAgent: string;
  semanticDescription: string;
  value: string | null;
  resolvedAt: string | null;
  stepIndex: number;
  // Inter-agent communication fields
  communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
  destinationAgents: string[];
  dependsOn: string[];
  dataFlowChain: Array<{
    agent: string;
    step: string;
    timestamp: string;
  }>;
  contextDependencies: string[];
  isSharedBetweenAgents: boolean;
}

export interface UseVariableTrackingProps {
  agent: any;
  workflowSteps: any[];
}

export function useVariableTracking({ agent, workflowSteps }: UseVariableTrackingProps) {
  const [variablePlaceholders, setVariablePlaceholders] = useState<VariablePlaceholder[]>([]);
  const [websocketStatus, setWebsocketStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [websocketConnection, setWebsocketConnection] = useState<any>(null);
  const [forceRender, setForceRender] = useState(0);

  // Discover variables for the current agent
  const discoverAgentVariables = useCallback(async (agentId: string) => {
    try {
      console.log("🔍 [VARIABLE DISCOVERY] Starting discovery for agent:", agentId);
      const response = await api.agents.discoverVariables(agentId);
      
      if (response.success && response.data?.variables) {
        const discoveredVariables = response.data.variables;
        console.log("🔍 [VARIABLE DISCOVERY] Found variables:", discoveredVariables);
        
        // Transform discovered variables to placeholder format
        const placeholders: VariablePlaceholder[] = discoveredVariables.map((variable: any, index: number) => ({
          id: `discovered-${agentId}-${index}`,
          placeholderName: variable.placeholder_name || variable.name || `{variable_${index}}`,
          sourceStep: variable.source_step || 'unknown',
          sourceAgent: variable.source_agent || agentId,
          semanticDescription: variable.semantic_description || variable.description || '',
          value: null,
          resolvedAt: null,
          stepIndex: variable.step_index || index,
          communicationType: 'internal',
          destinationAgents: [],
          dependsOn: [],
          dataFlowChain: [],
          contextDependencies: [],
          isSharedBetweenAgents: false
        }));
        
        setVariablePlaceholders(placeholders);
        console.log("🔍 [VARIABLE DISCOVERY] Set placeholders:", placeholders);
      }
    } catch (error) {
      console.error("🔍 [VARIABLE DISCOVERY] Failed to discover variables:", error);
    }
  }, []);

  // Detect inter-agent communication patterns
  const detectInterAgentCommunication = useCallback((
    placeholderName: string,
    sourceAgent: string,
    sourceStep: string,
    progressData: any
  ) => {
    const communicationInfo = {
      communicationType: 'internal' as 'inter-agent' | 'user-input' | 'system' | 'internal',
      destinationAgents: [] as string[],
      dependsOn: [] as string[],
      contextDependencies: [] as string[]
    };

    // Detect user input variables
    if (sourceAgent === 'user' || sourceStep === 'user_input' || placeholderName.includes('user.')) {
      communicationInfo.communicationType = 'user-input';
      // User input is typically used by the first agent in the workflow
      if (workflowSteps.length > 0) {
        communicationInfo.destinationAgents = [workflowSteps[0].assignee];
      }
    }
    // Detect system variables
    else if (sourceAgent === 'system' || placeholderName.includes('system.')) {
      communicationInfo.communicationType = 'system';
    }
    // Detect inter-agent communication
    else if (workflowSteps.length > 1) {
      const currentStepIndex = workflowSteps.findIndex(step => step.assignee === sourceAgent);
      if (currentStepIndex >= 0 && currentStepIndex < workflowSteps.length - 1) {
        // This variable might be used by subsequent steps
        communicationInfo.communicationType = 'inter-agent';
        communicationInfo.destinationAgents = workflowSteps
          .slice(currentStepIndex + 1)
          .map(step => step.assignee);
      }
    }

    // Detect dependencies based on placeholder naming patterns
    if (placeholderName.includes('.')) {
      const parts = placeholderName.split('.');
      if (parts.length > 1) {
        communicationInfo.contextDependencies = parts.slice(0, -1);
      }
    }

    return communicationInfo;
  }, [workflowSteps]);

  // Update variable placeholder with new value
  const updateVariablePlaceholder = useCallback((
    placeholderName: string,
    value: string,
    sourceAgent: string,
    sourceStep: string,
    stepIndex: number,
    progressData?: any
  ) => {
    console.log("🔍 [VARIABLE UPDATE] Updating placeholder:", {
      placeholderName,
      value,
      sourceAgent,
      sourceStep,
      stepIndex
    });

    const interAgentInfo = detectInterAgentCommunication(
      placeholderName,
      sourceAgent,
      sourceStep,
      progressData || {}
    );

    setVariablePlaceholders(prev => {
      const existingIndex = prev.findIndex(p => p.placeholderName === placeholderName);
      
      if (existingIndex >= 0) {
        // Update existing placeholder
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          value,
          resolvedAt: new Date().toISOString(),
          sourceAgent,
          sourceStep,
          stepIndex,
          ...interAgentInfo,
          isSharedBetweenAgents: interAgentInfo.communicationType === 'inter-agent',
          dataFlowChain: [
            ...updated[existingIndex].dataFlowChain,
            {
              agent: sourceAgent,
              step: sourceStep,
              timestamp: new Date().toISOString()
            }
          ]
        };
        return updated;
      } else {
        // Create new placeholder
        const newPlaceholder: VariablePlaceholder = {
          id: `runtime-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          placeholderName,
          sourceStep,
          sourceAgent,
          semanticDescription: `Runtime variable from ${sourceStep}`,
          value,
          resolvedAt: new Date().toISOString(),
          stepIndex,
          ...interAgentInfo,
          isSharedBetweenAgents: interAgentInfo.communicationType === 'inter-agent',
          dataFlowChain: [{
            agent: sourceAgent,
            step: sourceStep,
            timestamp: new Date().toISOString()
          }]
        };
        return [...prev, newPlaceholder];
      }
    });
  }, [detectInterAgentCommunication]);

  // Process progress data for variable updates
  const processProgressForVariables = useCallback((progressData: any) => {
    if (!progressData) return;

    const stepName = progressData.step_name || progressData.stepName || '';
    const assignee = progressData.assignee || progressData.member_name || 'unknown';
    const stepIndex = progressData.step || progressData.currentStep || 0;

    console.log("🔍 [VARIABLE DEBUG] Processing progress data:", {
      stepName,
      assignee,
      stepIndex,
      progressData
    });

    // Handle context_data (from backend context service)
    if (progressData.context_data) {
      console.log("🔍 [VARIABLE DEBUG] Processing context_data:", progressData.context_data);
      Object.entries(progressData.context_data).forEach(([key, value]) => {
        if (typeof value === 'string' && value.trim()) {
          const placeholderName = `{${key}}`;
          updateVariablePlaceholder(
            placeholderName,
            value,
            assignee,
            stepName,
            stepIndex,
            progressData
          );
        }
      });
    }

    // Handle variables_resolved (explicit variable tracking)
    if (progressData.variables_resolved && Array.isArray(progressData.variables_resolved)) {
      progressData.variables_resolved.forEach((variable: any) => {
        const placeholderName = variable.placeholder_name || variable.name;
        const sourceAgent = variable.source_agent || assignee;
        const sourceStep = variable.source_step || stepName;

        if (placeholderName && variable.value) {
          updateVariablePlaceholder(
            placeholderName,
            variable.value,
            sourceAgent,
            sourceStep,
            stepIndex,
            { ...progressData, ...variable }
          );
        }
      });
    }

    // Handle result output for implicit variable extraction
    if (progressData.result?.output && typeof progressData.result.output === 'string') {
      const output = progressData.result.output;
      
      // Extract variables from output using simple patterns
      const variablePatterns = [
        /\{([^}]+)\}/g, // {variable_name}
        /\$\{([^}]+)\}/g, // ${variable_name}
        /\[\[([^\]]+)\]\]/g // [[variable_name]]
      ];

      variablePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(output)) !== null) {
          const placeholderName = `{${match[1]}}`;
          // For implicit extraction, use a portion of the output as the value
          const value = output.substring(0, Math.min(100, output.length));
          
          updateVariablePlaceholder(
            placeholderName,
            value,
            assignee,
            stepName,
            stepIndex,
            progressData
          );
        }
      });
    }
  }, [updateVariablePlaceholder]);

  // WebSocket connection management
  const establishWebSocketConnection = useCallback(async (agentId: string) => {
    if (websocketStatus === 'connected' || websocketStatus === 'connecting') {
      console.log("🔌 [WebSocket] Already connected or connecting");
      return;
    }

    console.log("🔌 [WebSocket] Establishing connection for agent:", agentId);
    setWebsocketStatus('connecting');

    try {
      const callbacks: VariableTrackingCallbacks = {
        onVariableUpdate: (update: VariableUpdate) => {
          console.log("🔌 [WebSocket] Variable update received:", update);
          
          if (update.variables && Array.isArray(update.variables)) {
            update.variables.forEach((variable: any) => {
              updateVariablePlaceholder(
                variable.placeholder_name || variable.name,
                variable.value,
                variable.source_agent || 'websocket',
                variable.source_step || 'websocket_update',
                variable.step_index || 0,
                variable
              );
            });
          }
        },
        onConnectionStatusChange: (status: string) => {
          console.log("🔌 [WebSocket] Connection status changed:", status);
          setWebsocketStatus(status as any);
          setForceRender(prev => prev + 1);
        },
        onError: (error: any) => {
          console.error("🔌 [WebSocket] Error:", error);
          setWebsocketStatus('disconnected');
        }
      };

      // Get token from localStorage or API
      const token = localStorage.getItem('token') || 'default_token';

      const connection = createVariableTrackingWebSocket(agentId, token, callbacks);
      const connected = await connection.connect();

      if (connected) {
        setWebsocketConnection(connection);
        setWebsocketStatus('connected');
      } else {
        setWebsocketStatus('disconnected');
        throw new Error('Failed to establish WebSocket connection');
      }
      
      console.log("🔌 [WebSocket] Connection established successfully");
    } catch (error) {
      console.error("🔌 [WebSocket] Failed to establish connection:", error);
      setWebsocketStatus('disconnected');
    }
  }, [websocketStatus, updateVariablePlaceholder]);

  const disconnectWebSocket = useCallback(() => {
    if (websocketConnection) {
      console.log("🔌 [WebSocket] Disconnecting");
      websocketConnection.disconnect();
      setWebsocketConnection(null);
    }
    setWebsocketStatus('disconnected');
  }, [websocketConnection]);

  // Clear all variables
  const clearVariables = useCallback(() => {
    setVariablePlaceholders([]);
  }, []);

  // Auto-discover variables when agent changes
  useEffect(() => {
    if (agent?.agent_id) {
      console.log("🔍 [VARIABLE DISCOVERY] Agent changed, discovering variables for:", agent.agent_id);
      discoverAgentVariables(agent.agent_id);
    }
  }, [agent?.agent_id, discoverAgentVariables]);

  // Cleanup WebSocket on unmount
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  // Monitor WebSocket connection status
  useEffect(() => {
    console.log("🔌 [WebSocket] Status changed to:", websocketStatus);
    setForceRender(prev => prev + 1);
  }, [websocketStatus]);

  return {
    // State
    variablePlaceholders,
    websocketStatus,
    forceRender,
    
    // Actions
    discoverAgentVariables,
    updateVariablePlaceholder,
    processProgressForVariables,
    establishWebSocketConnection,
    disconnectWebSocket,
    clearVariables,
    detectInterAgentCommunication,
    
    // Setters
    setVariablePlaceholders
  };
}
