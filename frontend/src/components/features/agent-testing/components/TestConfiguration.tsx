/**
 * Test configuration component for agent testing interface
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Play,
  Square,
  RotateCcw,
  Settings,
  ChevronDown,
  Key,
  Zap,
  AlertCircle
} from 'lucide-react';
import { AIModelOverride } from '../ai-model-override';
import { type AIModelOverride as AIModelOverrideType } from '../ai-model-override';
import { APIKey } from '@/lib/types';

interface TestConfigurationProps {
  input: string;
  onInputChange: (value: string) => void;
  isLoading: boolean;
  onSubmit: () => void;
  onClear: () => void;
  onStop?: () => void;
  
  // AI Configuration
  aiOverride: AIModelOverrideType;
  onAiOverrideChange: (override: AIModelOverrideType) => void;
  apiKeys: APIKey[];
  loadingApiKeys: boolean;
  
  // Options
  options: any;
  onOptionsChange: (options: any) => void;
  
  // Agent info
  agent: any;
  
  // UI State
  showProgress: boolean;
  executionState: any;
}

export function TestConfiguration({
  input,
  onInputChange,
  isLoading,
  onSubmit,
  onClear,
  onStop,
  aiOverride,
  onAiOverrideChange,
  apiKeys,
  loadingApiKeys,
  options,
  onOptionsChange,
  agent,
  showProgress,
  executionState
}: TestConfigurationProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [inputError, setInputError] = useState<string | null>(null);

  // Validate input
  useEffect(() => {
    if (input.trim().length === 0) {
      setInputError(null);
    } else if (input.trim().length < 3) {
      setInputError("输入内容至少需要3个字符");
    } else if (input.trim().length > 10000) {
      setInputError("输入内容不能超过10000个字符");
    } else {
      setInputError(null);
    }
  }, [input]);

  const canSubmit = input.trim().length >= 3 && !isLoading && !inputError;
  const isRunning = executionState?.isRunning || isLoading;

  const handleSubmit = () => {
    if (canSubmit) {
      onSubmit();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Zap className="w-5 h-5 text-blue-500" />
              测试配置
            </CardTitle>
            <div className="flex items-center gap-2">
              {agent && (
                <Badge variant="outline" className="text-xs">
                  {agent.team_name}
                </Badge>
              )}
              {isRunning && (
                <Badge variant="secondary" className="text-xs animate-pulse">
                  运行中
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Input Section */}
          <div className="space-y-2">
            <Label htmlFor="test-input" className="text-sm font-medium">
              测试输入 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="test-input"
              placeholder="请输入要测试的内容..."
              value={input}
              onChange={(e) => onInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className={`min-h-[120px] resize-none ${
                inputError ? 'border-red-500 focus:border-red-500' : ''
              }`}
              disabled={isLoading}
            />
            {inputError && (
              <div className="flex items-center gap-1 text-sm text-red-500">
                <AlertCircle className="w-4 h-4" />
                {inputError}
              </div>
            )}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>支持 Ctrl+Enter 快速提交</span>
              <span>{input.length}/10000</span>
            </div>
          </div>

          {/* Basic Options */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="stream-mode" className="text-sm font-medium">
                流式响应
              </Label>
              <Switch
                id="stream-mode"
                checked={options.stream}
                onCheckedChange={(checked) => 
                  onOptionsChange({ ...options, stream: checked })
                }
                disabled={isLoading}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              启用后可以实时查看执行进度和响应内容
            </p>
          </div>

          <Separator />

          {/* Advanced Configuration */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-between p-0 h-auto"
                disabled={isLoading}
              >
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  <span className="text-sm font-medium">高级配置</span>
                </div>
                <ChevronDown className={`w-4 h-4 transition-transform ${
                  isAdvancedOpen ? 'rotate-180' : ''
                }`} />
              </Button>
            </CollapsibleTrigger>

            <CollapsibleContent className="space-y-4 mt-4">
              <AIModelOverride
                agent={agent}
                override={aiOverride}
                onOverrideChange={onAiOverrideChange}
                apiKeys={apiKeys}
              />
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit}
              className="flex-1"
              size="lg"
            >
              <Play className="w-4 h-4 mr-2" />
              {isLoading ? '执行中...' : '开始测试'}
            </Button>

            {isRunning && onStop && (
              <Button
                onClick={onStop}
                variant="destructive"
                size="lg"
              >
                <Square className="w-4 h-4 mr-2" />
                停止
              </Button>
            )}

            <Button
              onClick={onClear}
              variant="outline"
              disabled={isLoading}
              size="lg"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              清空
            </Button>
          </div>

          {/* Configuration Summary */}
          {aiOverride.enabled && (
            <div className="mt-4 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Key className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">AI配置覆盖</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                <div>
                  <span className="font-medium">提供商:</span> {aiOverride.provider}
                </div>
                <div>
                  <span className="font-medium">模型:</span> {aiOverride.model || '未设置'}
                </div>
                <div>
                  <span className="font-medium">温度:</span> {aiOverride.temperature}
                </div>
                <div>
                  <span className="font-medium">最大令牌:</span> {aiOverride.maxTokens}
                </div>
                {aiOverride.baseUrl && (
                  <div className="col-span-2">
                    <span className="font-medium">自定义URL:</span> {aiOverride.baseUrl}
                  </div>
                )}
                {aiOverride.apiKeyId && aiOverride.apiKeyId !== "default" && (
                  <div className="col-span-2">
                    <span className="font-medium">API密钥:</span> 
                    {apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId))?.name || '未知'}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Execution Status */}
          {showProgress && executionState && (
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800/50">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  执行状态
                </span>
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                {executionState.currentStage && (
                  <div>当前阶段: {executionState.currentStage}</div>
                )}
                {executionState.currentStep && (
                  <div>当前步骤: {executionState.currentStep}</div>
                )}
                {executionState.completedSteps.length > 0 && (
                  <div>已完成步骤: {executionState.completedSteps.length}</div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
