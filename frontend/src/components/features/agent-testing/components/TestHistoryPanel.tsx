/**
 * Test history panel component
 */

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  History,
  RefreshCw,
  Play,
  Trash2,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Timer,
  BarChart3
} from 'lucide-react';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';

interface TestHistoryItem {
  id: string;
  agent_id: string;
  input_text: string;
  output: string;
  status: 'completed' | 'failed' | 'running';
  created_at: string;
  execution_duration_ms?: number;
  ai_config?: any;
  metadata?: any;
  variables?: any[];
}

interface TestStatistics {
  total: number;
  completed: number;
  failed: number;
  running: number;
  successRate: number;
  avgDuration: number;
}

interface TestHistoryPanelProps {
  testHistory: TestHistoryItem[];
  loadingHistory: boolean;
  testStatistics: TestStatistics;
  onLoadDetail: (testId: string) => void;
  onRerunTest: (test: TestHistoryItem) => void;
  onDeleteTest: (testId: string) => void;
  onRefresh: () => void;
  onExport: (format: 'json' | 'csv') => void;
  onClear: () => void;
}

export function TestHistoryPanel({
  testHistory,
  loadingHistory,
  testStatistics,
  onLoadDetail,
  onRerunTest,
  onDeleteTest,
  onRefresh,
  onExport,
  onClear
}: TestHistoryPanelProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-pulse" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          label: '已完成',
          color: 'text-green-700 dark:text-green-300',
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          borderColor: 'border-green-200 dark:border-green-800'
        };
      case 'failed':
        return {
          label: '失败',
          color: 'text-red-700 dark:text-red-300',
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          borderColor: 'border-red-200 dark:border-red-800'
        };
      case 'running':
        return {
          label: '运行中',
          color: 'text-blue-700 dark:text-blue-300',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30',
          borderColor: 'border-blue-200 dark:border-blue-800'
        };
      default:
        return {
          label: '未知',
          color: 'text-gray-700 dark:text-gray-300',
          bgColor: 'bg-gray-100 dark:bg-gray-900/30',
          borderColor: 'border-gray-200 dark:border-gray-800'
        };
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="space-y-4"
    >
      {/* Statistics */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              测试统计
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={onRefresh} disabled={loadingHistory}>
                <RefreshCw className={`w-4 h-4 mr-1 ${loadingHistory ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button variant="outline" size="sm" onClick={() => onExport('json')}>
                <Download className="w-4 h-4 mr-1" />
                导出
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{testStatistics.total}</div>
              <div className="text-xs text-muted-foreground">总测试</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{testStatistics.completed}</div>
              <div className="text-xs text-muted-foreground">成功</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{testStatistics.failed}</div>
              <div className="text-xs text-muted-foreground">失败</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {testStatistics.successRate.toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">成功率</div>
            </div>
          </div>

          {testStatistics.avgDuration > 0 && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center gap-2">
                <Timer className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">平均执行时间:</span>
                <span className="text-sm text-muted-foreground">
                  {formatDuration(testStatistics.avgDuration)}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test History */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <History className="w-5 h-5 text-green-500" />
              测试历史
            </CardTitle>
            {testHistory.length > 0 && (
              <Button variant="destructive" size="sm" onClick={onClear}>
                <Trash2 className="w-4 h-4 mr-1" />
                清空
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {loadingHistory ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              ))}
            </div>
          ) : testHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <History className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>暂无测试历史</p>
              <p className="text-xs">执行测试后将显示历史记录</p>
            </div>
          ) : (
            <ScrollArea className="h-64 w-full">
              <div className="space-y-2">
                {testHistory.map((test) => {
                  const statusConfig = getStatusConfig(test.status);
                  const StatusIcon = test.status === 'completed' ? CheckCircle : 
                                   test.status === 'failed' ? XCircle : Clock;

                  return (
                    <Card
                      key={test.id}
                      className={`test-card-compact ${statusConfig.borderColor} h-fit hover:shadow-md transition-shadow`}
                    >
                      <CardContent className="p-3">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2 min-w-0 flex-1">
                            <div className={`p-1.5 rounded-md ${statusConfig.bgColor} shrink-0`}>
                              <StatusIcon className={`w-3.5 h-3.5 ${statusConfig.color}`} />
                            </div>
                            <div className="min-w-0 flex-1">
                              <Badge className={`${statusConfig.bgColor} ${statusConfig.color} border-0 text-xs px-2 py-0.5`}>
                                {statusConfig.label}
                              </Badge>
                            </div>
                          </div>
                          {test.execution_duration_ms && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground shrink-0">
                              <Timer className="w-3 h-3" />
                              {formatDuration(test.execution_duration_ms)}
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="space-y-2">
                          <div className="text-sm">
                            <div className="font-medium mb-1">输入:</div>
                            <div className="text-muted-foreground text-xs bg-muted/50 p-2 rounded">
                              {test.input_text.length > 80 
                                ? `${test.input_text.substring(0, 80)}...`
                                : test.input_text
                              }
                            </div>
                          </div>

                          {test.output && test.status === 'completed' && (
                            <div className="text-sm">
                              <div className="font-medium mb-1">输出:</div>
                              <div className="text-muted-foreground text-xs bg-muted/50 p-2 rounded">
                                {test.output.length > 80 
                                  ? `${test.output.substring(0, 80)}...`
                                  : test.output
                                }
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{format(new Date(test.created_at), 'MM-dd HH:mm')}</span>
                            {test.ai_config?.model && (
                              <Badge variant="outline" className="text-xs">
                                {test.ai_config.model}
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <Separator className="my-2" />
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onLoadDetail(test.id)}
                            className="h-7 px-2 text-xs"
                          >
                            <Eye className="w-3 h-3 mr-1" />
                            详情
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onRerunTest(test)}
                            className="h-7 px-2 text-xs"
                          >
                            <Play className="w-3 h-3 mr-1" />
                            重跑
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteTest(test.id)}
                            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3 mr-1" />
                            删除
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
