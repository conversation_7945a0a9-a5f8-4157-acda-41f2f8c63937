/**
 * Variable tracking panel component
 */

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  RefreshCw,
  Wifi,
  WifiOff,
  ArrowRightLeft,
  Users,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';

interface VariablePlaceholder {
  id: string;
  placeholderName: string;
  sourceStep: string;
  sourceAgent: string;
  semanticDescription: string;
  value: string | null;
  resolvedAt: string | null;
  stepIndex: number;
  communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
  destinationAgents: string[];
  dependsOn: string[];
  dataFlowChain: Array<{
    agent: string;
    step: string;
    timestamp: string;
  }>;
  contextDependencies: string[];
  isSharedBetweenAgents: boolean;
}

interface VariableTrackingPanelProps {
  variablePlaceholders: VariablePlaceholder[];
  websocketStatus: 'connected' | 'disconnected' | 'connecting';
  workflowSteps: any[];
  agent: any;
  onReconnect: () => void;
}

export function VariableTrackingPanel({
  variablePlaceholders,
  websocketStatus,
  workflowSteps,
  agent,
  onReconnect
}: VariableTrackingPanelProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <WifiOff className="w-4 h-4 text-red-500" />;
    }
  };

  const getCommunicationTypeIcon = (type: string) => {
    switch (type) {
      case 'inter-agent':
        return <ArrowRightLeft className="w-4 h-4 text-blue-500" />;
      case 'user-input':
        return <Users className="w-4 h-4 text-green-500" />;
      case 'system':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getCommunicationTypeLabel = (type: string) => {
    switch (type) {
      case 'inter-agent':
        return '代理间通信';
      case 'user-input':
        return '用户输入';
      case 'system':
        return '系统变量';
      default:
        return '内部变量';
    }
  };

  const sortedPlaceholders = [...variablePlaceholders].sort((a, b) => {
    // Sort by step index first, then by resolution status
    if (a.stepIndex !== b.stepIndex) {
      return a.stepIndex - b.stepIndex;
    }
    if (a.value && !b.value) return -1;
    if (!a.value && b.value) return 1;
    return 0;
  });

  const resolvedCount = variablePlaceholders.filter(p => p.value).length;
  const totalCount = variablePlaceholders.length;
  const interAgentCount = variablePlaceholders.filter(p => p.communicationType === 'inter-agent').length;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      className="space-y-4"
    >
      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              {getStatusIcon(websocketStatus)}
              变量跟踪
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {websocketStatus === 'connected' ? '已连接' : websocketStatus === 'connecting' ? '连接中' : '未连接'}
              </Badge>
              {websocketStatus === 'disconnected' && (
                <Button variant="outline" size="sm" onClick={onReconnect}>
                  <RefreshCw className="w-4 h-4 mr-1" />
                  重连
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Statistics */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{totalCount}</div>
              <div className="text-xs text-muted-foreground">总变量</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{resolvedCount}</div>
              <div className="text-xs text-muted-foreground">已解析</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{interAgentCount}</div>
              <div className="text-xs text-muted-foreground">代理间通信</div>
            </div>
          </div>

          <Separator />

          {/* Variables List */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">变量占位符</h4>
            
            {totalCount === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>暂无变量数据</p>
                <p className="text-xs">执行测试后将显示变量跟踪信息</p>
              </div>
            ) : (
              <ScrollArea className="h-64 w-full">
                <div className="space-y-2">
                  {sortedPlaceholders.map((placeholder) => (
                    <Card
                      key={placeholder.id}
                      className={`p-3 transition-all ${
                        placeholder.value
                          ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
                          : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950/30 dark:border-yellow-800/50'
                      }`}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <code className="text-sm font-mono bg-background/50 px-2 py-1 rounded border">
                              {placeholder.placeholderName}
                            </code>
                            <Badge variant="outline" className="text-xs">
                              步骤 {placeholder.stepIndex + 1}
                            </Badge>
                            {placeholder.isSharedBetweenAgents && (
                              <Badge variant="secondary" className="text-xs">
                                共享
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 mb-2">
                            {getCommunicationTypeIcon(placeholder.communicationType)}
                            <span className="text-xs text-muted-foreground">
                              {getCommunicationTypeLabel(placeholder.communicationType)}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              来源: {placeholder.sourceAgent}
                            </span>
                          </div>

                          {placeholder.semanticDescription && (
                            <p className="text-xs text-muted-foreground mb-2">
                              {placeholder.semanticDescription}
                            </p>
                          )}

                          {placeholder.value && (
                            <div className="mt-2">
                              <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">
                                已解析值:
                              </div>
                              <div className="text-xs bg-background/50 p-2 rounded border max-h-16 overflow-y-auto">
                                {placeholder.value.length > 100 
                                  ? `${placeholder.value.substring(0, 100)}...`
                                  : placeholder.value
                                }
                              </div>
                            </div>
                          )}

                          {placeholder.destinationAgents.length > 0 && (
                            <div className="mt-2">
                              <div className="text-xs font-medium mb-1">目标代理:</div>
                              <div className="flex flex-wrap gap-1">
                                {placeholder.destinationAgents.map((agent, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {agent}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {placeholder.dataFlowChain.length > 0 && (
                            <div className="mt-2">
                              <div className="text-xs font-medium mb-1">数据流:</div>
                              <div className="space-y-1">
                                {placeholder.dataFlowChain.map((flow, index) => (
                                  <div key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                                    <span>{flow.agent}</span>
                                    <span>→</span>
                                    <span>{flow.step}</span>
                                    <span className="ml-auto">
                                      {format(new Date(flow.timestamp), 'HH:mm:ss')}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-1">
                          {placeholder.value ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <Clock className="w-4 h-4 text-yellow-500" />
                          )}
                        </div>
                      </div>

                      {placeholder.resolvedAt && (
                        <div className="mt-2 pt-2 border-t border-background/50">
                          <div className="text-xs text-muted-foreground">
                            解析时间: {format(new Date(placeholder.resolvedAt), 'yyyy-MM-dd HH:mm:ss')}
                          </div>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
