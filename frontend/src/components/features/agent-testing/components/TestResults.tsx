/**
 * Test results component for displaying execution results and progress
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Copy,
  Eye,
  Timer,
  Cpu,
  DollarSign
} from 'lucide-react';
import { StreamingMarkdownRenderer } from '@/components/ui/markdown-renderer';
import { JsonFormatter } from '@/components/ui/json-formatter';
import { format } from 'date-fns';

interface TestResultsProps {
  response: string | null;
  responseMetadata: any;
  realtimeResponse: string;
  showProgress: boolean;
  progress: any;
  executionState: any;
  progressUpdates: any[];
  executionStages: any[];
  isLoading: boolean;
}

export function TestResults({
  response,
  responseMetadata,
  realtimeResponse,
  showProgress,
  progress,
  executionState,
  progressUpdates,
  executionStages,
  isLoading
}: TestResultsProps) {
  const [activeTab, setActiveTab] = useState("result");
  const [showRawResponse, setShowRawResponse] = useState(false);

  const hasResults = response || realtimeResponse || executionStages.length > 0;
  const isRunning = executionState?.isRunning || isLoading;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-pulse" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  if (!hasResults && !showProgress && !isRunning) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex items-center justify-center h-64 text-muted-foreground"
      >
        <div className="text-center">
          <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>执行测试后，结果将在这里显示</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="space-y-4"
    >
      {/* Progress Section */}
      {showProgress && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-500" />
              执行进度
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {progress.totalSteps > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>步骤进度</span>
                  <span>{progress.currentStep}/{progress.totalSteps}</span>
                </div>
                <Progress 
                  value={(progress.currentStep / progress.totalSteps) * 100} 
                  className="h-2"
                />
              </div>
            )}
            
            {progress.stepName && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  当前步骤
                </Badge>
                <span className="text-sm">{progress.stepName}</span>
                {progress.assignee && (
                  <Badge variant="secondary" className="text-xs">
                    {progress.assignee}
                  </Badge>
                )}
              </div>
            )}

            {progress.message && (
              <p className="text-sm text-muted-foreground">{progress.message}</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Results Section */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              {getStatusIcon(executionState?.hasResults ? 'completed' : isRunning ? 'running' : 'pending')}
              测试结果
            </CardTitle>
            <div className="flex items-center gap-2">
              {responseMetadata?.execution_duration_ms && (
                <Badge variant="outline" className="text-xs">
                  <Timer className="w-3 h-3 mr-1" />
                  {formatDuration(responseMetadata.execution_duration_ms)}
                </Badge>
              )}
              {response && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(response)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="result">结果</TabsTrigger>
              <TabsTrigger value="progress">进度详情</TabsTrigger>
              <TabsTrigger value="metadata">元数据</TabsTrigger>
            </TabsList>

            <TabsContent value="result" className="mt-4">
              <div className="space-y-4">
                {/* Real-time Response */}
                {realtimeResponse && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">实时响应</h4>
                      <Badge variant="secondary" className="text-xs animate-pulse">
                        实时更新
                      </Badge>
                    </div>
                    <ScrollArea className="h-64 w-full rounded-md border p-4">
                      <StreamingMarkdownRenderer content={realtimeResponse} />
                    </ScrollArea>
                  </div>
                )}

                {/* Final Response */}
                {response && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">最终结果</h4>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowRawResponse(!showRawResponse)}
                        >
                          {showRawResponse ? '格式化' : '原始'}
                        </Button>
                      </div>
                    </div>
                    <ScrollArea className="max-h-96 w-full rounded-md border p-4">
                      {showRawResponse ? (
                        <pre className="text-sm whitespace-pre-wrap">{response}</pre>
                      ) : (
                        <StreamingMarkdownRenderer content={response} />
                      )}
                    </ScrollArea>
                  </div>
                )}

                {/* Execution Stages */}
                {executionStages.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">执行阶段</h4>
                    <div className="space-y-2">
                      {executionStages.map((stage, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(stage.status)}
                              <span className="text-sm font-medium">{stage.step_name}</span>
                              {stage.assignee && (
                                <Badge variant="outline" className="text-xs">
                                  {stage.assignee}
                                </Badge>
                              )}
                            </div>
                            {stage.executed_at && (
                              <span className="text-xs text-muted-foreground">
                                {format(new Date(stage.executed_at), 'HH:mm:ss')}
                              </span>
                            )}
                          </div>
                          {stage.output && (
                            <ScrollArea className="max-h-32 w-full">
                              <div className="text-sm text-muted-foreground">
                                {stage.output.length > 200 
                                  ? `${stage.output.substring(0, 200)}...`
                                  : stage.output
                                }
                              </div>
                            </ScrollArea>
                          )}
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="progress" className="mt-4">
              <ScrollArea className="h-64 w-full">
                <div className="space-y-2">
                  {progressUpdates.map((update, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">
                          {update.step_name || update.stage || `更新 ${index + 1}`}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(update.timestamp), 'HH:mm:ss.SSS')}
                        </span>
                      </div>
                      {update.message && (
                        <p className="text-xs text-muted-foreground">{update.message}</p>
                      )}
                      {update.progress !== undefined && (
                        <Progress value={update.progress} className="h-1 mt-2" />
                      )}
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="metadata" className="mt-4">
              <div className="space-y-4">
                {responseMetadata && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">响应元数据</h4>
                    <JsonFormatter data={responseMetadata} />
                  </div>
                )}

                {/* Execution Statistics */}
                {responseMetadata && (
                  <div className="grid grid-cols-2 gap-4">
                    {responseMetadata.execution_duration_ms && (
                      <div className="flex items-center gap-2">
                        <Timer className="w-4 h-4 text-blue-500" />
                        <div>
                          <div className="text-sm font-medium">执行时间</div>
                          <div className="text-xs text-muted-foreground">
                            {formatDuration(responseMetadata.execution_duration_ms)}
                          </div>
                        </div>
                      </div>
                    )}

                    {responseMetadata.token_usage && (
                      <div className="flex items-center gap-2">
                        <Cpu className="w-4 h-4 text-green-500" />
                        <div>
                          <div className="text-sm font-medium">令牌使用</div>
                          <div className="text-xs text-muted-foreground">
                            {responseMetadata.token_usage.total_tokens || 'N/A'}
                          </div>
                        </div>
                      </div>
                    )}

                    {responseMetadata.cost && (
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-yellow-500" />
                        <div>
                          <div className="text-sm font-medium">成本</div>
                          <div className="text-xs text-muted-foreground">
                            ${responseMetadata.cost.toFixed(4)}
                          </div>
                        </div>
                      </div>
                    )}

                    {responseMetadata.ai_config && (
                      <div className="flex items-center gap-2">
                        <Cpu className="w-4 h-4 text-purple-500" />
                        <div>
                          <div className="text-sm font-medium">AI模型</div>
                          <div className="text-xs text-muted-foreground">
                            {responseMetadata.ai_config.model || 'Default'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}
