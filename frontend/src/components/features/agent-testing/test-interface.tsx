/**
 * Refactored Test Interface - Main component using modular hooks and components
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { api } from '@/lib/api';
import { type AIModelOverride as AIModelOverrideType } from './ai-model-override';
import { TestCaseManager, type TestCase } from './test-case-manager';
import { BatchTesting } from './batch-testing';
import { UnifiedTestDetailDialog } from '../test-history/unified-test-detail-dialog';
import { useKeyboardShortcuts, createTestInterfaceShortcuts, useKeyboardShortcutsHelp } from '@/hooks/use-keyboard-shortcuts';
import { APIKey } from '@/lib/types';
import { getAgentWorkflowSteps } from '@/lib/utils';
import {
  TestTube,
  Zap,
  Settings,
  History,
  ArrowRightLeft,
  Activity
} from 'lucide-react';

// Import our new modular components and hooks
import { useTestExecution } from './hooks/useTestExecution';
import { useVariableTracking } from './hooks/useVariableTracking';
import { useTestHistory } from './hooks/useTestHistory';
import { TestConfiguration } from './components/TestConfiguration';
import { TestResults } from './components/TestResults';
import { VariableTrackingPanel } from './components/VariableTrackingPanel';
import { TestHistoryPanel } from './components/TestHistoryPanel';

interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  api_endpoint: string;
  created_at?: string;
  team_plan?: {
    team_members: Array<{
      role: string;
      context_placeholders?: Array<{
        placeholder_name?: string;
        name?: string;
        source_step?: string;
        source_agent_role?: string;
        semantic_description?: string;
        description?: string;
      }>;
    }>;
  };
}

interface TestInterfaceProps {
  agent: Agent;
  agents: Agent[];
  onTestSubmit: (input: string, options: any, aiOverride?: AIModelOverrideType, responseMetadata?: any) => void;
  onAgentChange?: () => void;
  initialParams?: {
    input_text?: string | null;
    ai_config?: string | null;
    api_key_name?: string | null;
  };
}

export function TestInterface({ agent, agents, onAgentChange, initialParams }: TestInterfaceProps) {
  // Basic state
  const [input, setInput] = useState("");
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loadingApiKeys, setLoadingApiKeys] = useState(true);
  const [aiOverride, setAiOverride] = useState<AIModelOverrideType>({
    enabled: false,
    provider: "openai",
    model: "",
    temperature: 0.7,
    maxTokens: 2000,
    baseUrl: undefined,
    customProviderName: undefined,
    apiKeyId: undefined
  });
  const [options, setOptions] = useState({
    stream: true // Default to streaming for progress updates
  });

  // Tab management
  const [mainActiveTab, setMainActiveTab] = useState("test");
  const [workflowSteps, setWorkflowSteps] = useState<any[]>([]);

  // Initialize hooks
  const testExecution = useTestExecution({
    agent,
    onProgressUpdate: (progress) => {
      variableTracking.processProgressForVariables(progress);
    }
  });

  const variableTracking = useVariableTracking({
    agent,
    workflowSteps
  });

  const testHistory = useTestHistory({
    agent,
    autoRefresh: false
  });

  // Load API keys on mount
  useEffect(() => {
    const loadApiKeys = async () => {
      try {
        setLoadingApiKeys(true);
        const response = await api.apiKeys.list();
        if (response.success && response.data) {
          setApiKeys(response.data);
        }
      } catch (error) {
        console.error("Failed to load API keys:", error);
      } finally {
        setLoadingApiKeys(false);
      }
    };

    loadApiKeys();
  }, []);

  // Initialize workflow steps when agent changes
  useEffect(() => {
    if (agent?.team_plan) {
      const steps = getAgentWorkflowSteps(agent.team_plan);
      setWorkflowSteps(steps);
    }
  }, [agent]);

  // Apply initial parameters
  useEffect(() => {
    if (initialParams) {
      if (initialParams.input_text) {
        setInput(initialParams.input_text);
      }
      
      if (initialParams.ai_config) {
        try {
          const config = JSON.parse(initialParams.ai_config);
          setAiOverride(prev => ({
            ...prev,
            enabled: true,
            ...config
          }));
        } catch (error) {
          console.error("Failed to parse AI config:", error);
        }
      }

      if (initialParams.api_key_name && apiKeys.length > 0) {
        const matchingKey = apiKeys.find(key => key.name === initialParams.api_key_name);
        if (matchingKey) {
          setAiOverride(prev => ({
            ...prev,
            apiKeyId: String(matchingKey.id)
          }));
        }
      }
    }
  }, [initialParams, apiKeys]);

  // Establish WebSocket connection when needed
  useEffect(() => {
    if (agent?.agent_id && variableTracking.websocketStatus === 'disconnected') {
      variableTracking.establishWebSocketConnection(agent.agent_id);
    }
  }, [agent?.agent_id]);

  // Handle test submission
  const handleSubmit = async () => {
    if (!input.trim() || testExecution.isLoading) {
      return;
    }

    // Establish WebSocket connection for real-time variable tracking
    if (agent?.agent_id && variableTracking.websocketStatus === 'disconnected') {
      console.log("🔌 [WebSocket] Establishing connection before execution");
      await variableTracking.establishWebSocketConnection(agent.agent_id);
    }

    // Clear previous variables
    variableTracking.clearVariables();

    // Execute the test
    await testExecution.executeTest(input, options, aiOverride, apiKeys);

    // Refresh test history after execution
    setTimeout(() => {
      testHistory.loadTestHistory();
    }, 1000);

    // Switch to results tab
    setMainActiveTab("results");
  };

  // Handle clear
  const handleClear = () => {
    setInput("");
    testExecution.resetExecution();
    variableTracking.clearVariables();
    setMainActiveTab("test");
  };

  // Handle test case operations
  const handleLoadTestCase = (testCase: TestCase) => {
    setInput(testCase.input);
    if (testCase.aiOverride) {
      setAiOverride(testCase.aiOverride);
    }
  };

  const handleRunTestCase = async (testCase: TestCase) => {
    setInput(testCase.input);
    if (testCase.aiOverride) {
      setAiOverride(testCase.aiOverride);
    }
    // Wait a bit for state to update, then run the test
    setTimeout(() => {
      handleSubmit();
    }, 100);
  };

  // Set up keyboard shortcuts
  useKeyboardShortcutsHelp();
  const shortcuts = createTestInterfaceShortcuts({
    onSubmit: () => !testExecution.isLoading && input.trim() && handleSubmit(),
    onClear: handleClear,
    onSave: () => {}, // Will be implemented with test case manager
    onExport: () => {}, // Will be implemented with export functionality
    onToggleOverride: () => {} // Advanced config is now always accessible when expanded
  });
  useKeyboardShortcuts(shortcuts, true);

  return (
    <div className="space-y-6">
      {/* Test Case Management and Batch Testing */}
      <motion.div
        className="flex gap-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <TestCaseManager
          agents={agents as any}
          currentInput={input}
          currentAiOverride={aiOverride}
          selectedAgent={agent as any}
          onLoadTestCase={handleLoadTestCase}
          onRunTestCase={handleRunTestCase}
        />
        <BatchTesting
          agent={agent as any}
          agents={agents as any}
        />
      </motion.div>

      {/* Main Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Tabs value={mainActiveTab} onValueChange={setMainActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 mobile-tabs">
            <TabsTrigger
              value="test"
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <TestTube className="h-4 w-4 flex-shrink-0" />
              <span className="truncate text-xs sm:text-sm">测试</span>
            </TabsTrigger>
            <TabsTrigger
              value="results"
              disabled={!testExecution.isLoading && !testExecution.response}
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <Zap className={`h-4 w-4 flex-shrink-0 ${testExecution.isLoading ? 'animate-pulse text-blue-500' : testExecution.response ? 'text-green-500' : ''}`} />
              <span className="truncate text-xs sm:text-sm">结果</span>
              {testExecution.isLoading && (
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse ml-1 flex-shrink-0"></div>
              )}
              {testExecution.response && !testExecution.isLoading && (
                <div className="w-2 h-2 bg-green-500 rounded-full ml-1 flex-shrink-0"></div>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="variables"
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <ArrowRightLeft className="h-4 w-4 flex-shrink-0" />
              <span className="truncate text-xs sm:text-sm">变量</span>
              {variableTracking.websocketStatus === 'connected' && (
                <div className="w-2 h-2 bg-green-500 rounded-full ml-1 flex-shrink-0"></div>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <History className="h-4 w-4 flex-shrink-0" />
              <span className="truncate text-xs sm:text-sm">历史</span>
            </TabsTrigger>
          </TabsList>

          {/* Test Tab Content */}
          <TabsContent value="test" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              <TestConfiguration
                input={input}
                onInputChange={setInput}
                isLoading={testExecution.isLoading}
                onSubmit={handleSubmit}
                onClear={handleClear}
                onStop={testExecution.stopExecution}
                aiOverride={aiOverride}
                onAiOverrideChange={setAiOverride}
                apiKeys={apiKeys}
                loadingApiKeys={loadingApiKeys}
                options={options}
                onOptionsChange={setOptions}
                agent={agent}
                showProgress={testExecution.showProgress}
                executionState={testExecution.executionState}
              />
            </motion.div>
          </TabsContent>

          {/* Execution Tab Content */}
          <TabsContent value="execution" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              <TestResults
                response={testExecution.response}
                responseMetadata={testExecution.responseMetadata}
                realtimeResponse={testExecution.realtimeResponse}
                showProgress={testExecution.showProgress}
                progress={testExecution.progress}
                executionState={testExecution.executionState}
                progressUpdates={testExecution.progressUpdates}
                executionStages={testExecution.executionStages}
                isLoading={testExecution.isLoading}
              />
            </motion.div>
          </TabsContent>

          {/* Results Tab Content */}
          <TabsContent value="results" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              <TestResults
                response={testExecution.response}
                responseMetadata={testExecution.responseMetadata}
                realtimeResponse={testExecution.realtimeResponse}
                showProgress={testExecution.showProgress}
                progress={testExecution.progress}
                executionState={testExecution.executionState}
                progressUpdates={testExecution.progressUpdates}
                executionStages={testExecution.executionStages}
                isLoading={testExecution.isLoading}
              />
            </motion.div>
          </TabsContent>

          {/* Variables Tab Content */}
          <TabsContent value="variables" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              <VariableTrackingPanel
                variablePlaceholders={variableTracking.variablePlaceholders}
                websocketStatus={variableTracking.websocketStatus}
                workflowSteps={workflowSteps}
                agent={agent}
                onReconnect={() => variableTracking.establishWebSocketConnection(agent.agent_id)}
              />
            </motion.div>
          </TabsContent>

          {/* History Tab Content */}
          <TabsContent value="history" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              <TestHistoryPanel
                testHistory={testHistory.testHistory}
                loadingHistory={testHistory.loadingHistory}
                testStatistics={testHistory.testStatistics}
                onLoadDetail={testHistory.loadTestDetail}
                onRerunTest={(test) => testHistory.rerunTest(test, (input, options, aiOverride) => {
                  setInput(input);
                  if (aiOverride) setAiOverride(aiOverride);
                  setOptions(options);
                  setTimeout(handleSubmit, 100);
                })}
                onDeleteTest={testHistory.deleteTest}
                onRefresh={() => testHistory.loadTestHistory()}
                onExport={testHistory.exportHistory}
                onClear={testHistory.clearHistory}
              />
            </motion.div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Test Detail Dialog */}
      {testHistory.showDetailDialog && testHistory.selectedTest && (
        <UnifiedTestDetailDialog
          test={testHistory.selectedTest}
          open={testHistory.showDetailDialog}
          onClose={testHistory.closeDetailDialog}
        />
      )}
    </div>
  );
}
