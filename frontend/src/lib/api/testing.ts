/**
 * Testing and test history related API endpoints
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import { ApiResponse, Task, TestRecord } from "../types";

export class TestingAPIClient extends BaseAPIClient {
  // 获取任务状态
  async getTaskStatus(taskId: string): Promise<ApiResponse<Task>> {
    if (USE_MOCK_API) {
      return mockAPI.getTaskStatus(taskId);
    }

    return super.get<Task>(`/api/v1/agents/${taskId}/history`);
  }

  // 开始测试执行
  async startTestExecution(data: any): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/test-execution/start", data);
  }

  // 获取测试执行状态
  async getTestExecutionStatus(testId: string): Promise<ApiResponse<any>> {
    return super.get<any>(`/api/v1/test-execution/${testId}/status`);
  }

  // 流式获取测试执行结果
  async streamTestExecution(testId: string): Promise<ApiResponse<any>> {
    return super.get<any>(`/api/v1/test-execution/${testId}/stream`);
  }

  // 停止测试执行
  async stopTestExecution(testId: string): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/test-execution/${testId}/stop`);
  }

  // 创建测试历史记录
  async createTestHistory(data: any): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/test-history", data);
  }

  // 更新测试历史记录
  async updateTestHistory(testId: string, data: any): Promise<ApiResponse<any>> {
    return this.put<any>(`/api/v1/test-history/${testId}`, data);
  }

  // 获取测试历史列表
  async listTestHistory(params?: any): Promise<ApiResponse<any[]>> {
    return super.get<any[]>("/api/v1/test-history", params);
  }

  // 获取测试历史详情
  async getTestHistoryDetail(testId: string): Promise<ApiResponse<any>> {
    return super.get<any>(`/api/v1/test-history/${testId}`);
  }

  // 删除测试历史记录
  async deleteTestHistory(testId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/test-history/${testId}`);
  }

  // 根据Agent获取测试历史
  async getTestHistoryByAgent(agentId: string, params?: any): Promise<ApiResponse<any[]>> {
    return super.get<any[]>(`/api/v1/test-history/agent/${agentId}`, params);
  }

  // 获取最近的测试历史
  async getRecentTestHistory(): Promise<ApiResponse<any[]>> {
    return super.get<any[]>("/api/v1/test-history/recent");
  }

  // 导出测试历史
  async exportTestHistory(params?: {
    agent_id?: string;
    format?: 'json' | 'csv';
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<any>> {
    return super.get<any>("/api/v1/test-history/export", params);
  }

  // 清空测试历史
  async clearTestHistory(agentId?: string): Promise<ApiResponse<{ message: string }>> {
    const url = agentId 
      ? `/api/v1/test-history/clear?agent_id=${agentId}`
      : "/api/v1/test-history/clear";
    return this.post<{ message: string }>(url);
  }

  // 获取测试统计
  async getTestStatistics(agentId?: string): Promise<ApiResponse<any>> {
    const params = agentId ? { agent_id: agentId } : {};
    return super.get<any>("/api/v1/test-history/stats", params);
  }

  // 重新运行测试
  async rerunTest(testId: string): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/test-history/${testId}/rerun`);
  }

  // 比较测试结果
  async compareTests(testIds: string[]): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/test-history/compare", { test_ids: testIds });
  }

  // 获取测试性能指标
  async getTestPerformanceMetrics(params?: {
    agent_id?: string;
    start_date?: string;
    end_date?: string;
    metric_type?: string;
  }): Promise<ApiResponse<any>> {
    return super.get<any>("/api/v1/test-history/metrics", params);
  }

  // 批量删除测试历史
  async batchDeleteTestHistory(testIds: string[]): Promise<ApiResponse<{ deleted_count: number }>> {
    return this.post<{ deleted_count: number }>("/api/v1/test-history/batch-delete", {
      test_ids: testIds
    });
  }

  // 获取测试执行趋势
  async getTestExecutionTrends(params?: {
    agent_id?: string;
    period?: 'day' | 'week' | 'month';
    limit?: number;
  }): Promise<ApiResponse<any[]>> {
    return super.get<any[]>("/api/v1/test-history/trends", params);
  }

  // 获取测试失败分析
  async getTestFailureAnalysis(params?: {
    agent_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<any>> {
    return super.get<any>("/api/v1/test-history/failure-analysis", params);
  }

  // 创建测试套件
  async createTestSuite(data: {
    name: string;
    description?: string;
    agent_id: string;
    test_cases: any[];
  }): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/test-suites", data);
  }

  // 运行测试套件
  async runTestSuite(suiteId: string): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/test-suites/${suiteId}/run`);
  }

  // 获取测试套件列表
  async getTestSuites(agentId?: string): Promise<ApiResponse<any[]>> {
    const params = agentId ? { agent_id: agentId } : {};
    return super.get<any[]>("/api/v1/test-suites", params);
  }

  // 获取测试套件详情
  async getTestSuite(suiteId: string): Promise<ApiResponse<any>> {
    return super.get<any>(`/api/v1/test-suites/${suiteId}`);
  }

  // 更新测试套件
  async updateTestSuite(suiteId: string, data: any): Promise<ApiResponse<any>> {
    return this.put<any>(`/api/v1/test-suites/${suiteId}`, data);
  }

  // 删除测试套件
  async deleteTestSuite(suiteId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/test-suites/${suiteId}`);
  }
}

// 导出单例实例
export const testingAPI = new TestingAPIClient();
