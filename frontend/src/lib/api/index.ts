/**
 * Main API client - Aggregates all API modules
 */

import { agent<PERSON><PERSON>, AgentAP<PERSON>lient } from "./agents";
import { templateAPI, TemplateAPIClient } from "./templates";
import { testingAPI, TestingAPIClient } from "./testing";
import { authAPI, AuthAPIClient } from "./auth";
import { misc<PERSON><PERSON>, MiscAPIClient } from "./misc";
import { planningAPI, PlanningAPIClient } from "./planning";
import { BaseAPIClient } from "./base";

// Main API client class that aggregates all modules
export class APIClient extends BaseAPIClient {
  // API modules
  public agents: AgentAPIClient;
  public templates: TemplateAPIClient;
  public testing: TestingAPIClient;
  public auth: AuthAPIClient;
  public misc: MiscAPIClient;
  public planning: PlanningAPIClient;

  constructor() {
    super();
    
    // Initialize all API modules
    this.agents = agentAPI;
    this.templates = templateAPI;
    this.testing = testingAPI;
    this.auth = authAPI;
    this.misc = miscAPI;
    this.planning = planningAPI;
  }

  // Legacy compatibility methods - delegate to appropriate modules
  
  // Agent methods
  async getAgents(filters?: any, pagination?: any) {
    return this.agents.getAgents(filters, pagination);
  }

  async getAgent(id: string) {
    return this.agents.getAgent(id);
  }

  async createAgent(request: any) {
    return this.agents.createAgent(request);
  }

  async create(request: any) {
    return this.agents.createAgent(request);
  }

  async createAgentFromTemplate(templateId: string, customizations?: any) {
    return this.agents.createAgentFromTemplate(templateId, customizations);
  }

  async updateAgent(id: string, updates: any) {
    return this.agents.updateAgent(id, updates);
  }

  async updateStatus(id: string, status: any) {
    return this.agents.updateAgentStatus(id, status);
  }

  async deleteAgent(id: string) {
    return this.agents.deleteAgent(id);
  }

  async invokeAgent(agentId: string, request: any) {
    return this.agents.invokeAgent(agentId, request);
  }

  async toggleAgentFavorite(agentId: string) {
    return this.agents.toggleAgentFavorite(agentId);
  }

  async getFavoriteAgents(includePerformance: boolean = true) {
    return this.agents.getFavoriteAgents(includePerformance);
  }

  async discoverAgentVariables(agentId: string) {
    return this.agents.discoverAgentVariables(agentId);
  }

  // Template methods
  async getTemplates(filters?: any, page?: number, limit?: number) {
    return this.templates.getTemplates(filters, page, limit);
  }

  async getTemplate(id: string) {
    return this.templates.getTemplate(id);
  }

  async createTemplate(request: any) {
    return this.templates.createTemplate(request);
  }

  async createTemplateFromAgent(request: any) {
    return this.templates.createTemplateFromAgent(request);
  }

  async updateTemplate(id: string, updates: any) {
    return this.templates.updateTemplate(id, updates);
  }

  async deleteTemplate(id: string) {
    return this.templates.deleteTemplate(id);
  }

  async searchTemplates(query: string, filters?: any) {
    return this.templates.searchTemplates(query, filters);
  }

  async getTemplateCategories() {
    return this.templates.getTemplateCategories();
  }

  async getFeaturedTemplates(limit?: number) {
    return this.templates.getFeaturedTemplates(limit);
  }

  // Testing methods
  async getTaskStatus(taskId: string) {
    return this.testing.getTaskStatus(taskId);
  }

  async startTestExecution(data: any) {
    return this.testing.startTestExecution(data);
  }

  async getTestExecutionStatus(testId: string) {
    return this.testing.getTestExecutionStatus(testId);
  }

  async streamTestExecution(testId: string) {
    return this.testing.streamTestExecution(testId);
  }

  async createTestHistory(data: any) {
    return this.testing.createTestHistory(data);
  }

  async listTestHistory(params?: any) {
    return this.testing.listTestHistory(params);
  }

  async getTestHistoryDetail(testId: string) {
    return this.testing.getTestHistoryDetail(testId);
  }

  async deleteTestHistory(testId: string) {
    return this.testing.deleteTestHistory(testId);
  }

  async exportTestHistory(params?: any) {
    return this.testing.exportTestHistory(params);
  }

  // Auth methods
  async register(request: any) {
    return this.auth.register(request);
  }

  async login(request: any) {
    return this.auth.login(request);
  }

  async logout() {
    return this.auth.logout();
  }

  async getCurrentUser() {
    return this.auth.getCurrentUser();
  }

  async updateProfile(updates: any) {
    return this.auth.updateProfile(updates);
  }

  async changePassword(request: any) {
    return this.auth.changePassword(request);
  }

  async uploadAvatar(file: File) {
    return this.auth.uploadAvatar(file);
  }

  // API Keys methods
  async getApiKeys() {
    return this.misc.getApiKeys();
  }

  async createApiKey(data: any) {
    return this.misc.createApiKey(data);
  }

  async updateApiKey(id: number, data: any) {
    return this.misc.updateApiKey(id, data);
  }

  async deleteApiKey(id: number) {
    return this.misc.deleteApiKey(id);
  }

  // Dashboard methods
  async getDashboardData() {
    return this.misc.getDashboardData();
  }

  async getDashboardStats() {
    return this.misc.getDashboardStats();
  }

  // API Keys object for backward compatibility
  apiKeys = {
    list: () => this.getApiKeys(),
    create: (data: any) => this.createApiKey(data),
    update: (id: number, data: any) => this.updateApiKey(id, data),
    delete: (id: number) => this.deleteApiKey(id),
  };

  // Test History object for backward compatibility
  testHistory = {
    list: (params?: any) => this.listTestHistory(params),
    create: (data: any) => this.createTestHistory(data),
    getDetail: (testId: string) => this.getTestHistoryDetail(testId),
    delete: (testId: string) => this.deleteTestHistory(testId),
    export: (params?: any) => this.exportTestHistory(params),
  };

  // Test Execution object for backward compatibility
  testExecution = {
    start: (data: any) => this.startTestExecution(data),
    getStatus: (testId: string) => this.getTestExecutionStatus(testId),
    stream: (testId: string) => this.streamTestExecution(testId),
    stop: (testId: string) => this.testing.stopTestExecution(testId),
  };

  // Logs methods
  async getLogs(params?: any) {
    return this.misc.getLogs(params);
  }

  async getLogStats() {
    return this.misc.getLogStats();
  }

  // System methods
  async getSystemHealth() {
    return this.misc.getSystemHealth();
  }

  async getSystemInfo() {
    return this.misc.getSystemInfo();
  }

  // Planning methods
  async analyzeRequirements(userDescription: string) {
    return this.planning.analyze(userDescription);
  }

  async createPlanningRequest(userDescription: string, templateId?: string) {
    return this.planning.create(userDescription, templateId);
  }
}

// Create and export the main API instance
export const api = new APIClient();

// Export individual modules for direct access
export { agentAPI, templateAPI, testingAPI, authAPI, miscAPI, planningAPI };

// Export types and classes
export * from "./base";
export * from "./agents";
export * from "./templates";
export * from "./testing";
export * from "./auth";
export * from "./misc";
export * from "./planning";

// Legacy compatibility - export the main api instance as default
export default api;
