/**
 * Miscellaneous API endpoints (API Keys, Logs, Dashboard, etc.)
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import { ApiResponse, APIKey } from "../types";

export class MiscAPIClient extends BaseAPIClient {
  // API Keys管理
  async getApiKeys(): Promise<ApiResponse<APIKey[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getApiKeys();
    }

    return this.get<APIKey[]>("/api/v1/api-keys");
  }

  async createApiKey(data: {
    name: string;
    key_value: string;
  }): Promise<ApiResponse<APIKey>> {
    return this.post<APIKey>("/api/v1/api-keys", data);
  }

  async updateApiKey(id: number, data: {
    name?: string;
    key_value?: string;
  }): Promise<ApiResponse<APIKey>> {
    return this.put<APIKey>(`/api/v1/api-keys/${id}`, data);
  }

  async deleteApiKey(id: number): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/api-keys/${id}`);
  }

  // 日志管理
  async getLogs(params?: {
    level?: string;
    start_date?: string;
    end_date?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/logs", params);
  }

  async getLogStats(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/logs/stats");
  }

  async clearLogs(before_date?: string): Promise<ApiResponse<{ message: string }>> {
    const params = before_date ? { before_date } : {};
    return this.post<{ message: string }>("/api/v1/logs/clear", params);
  }

  async exportLogs(params?: {
    level?: string;
    start_date?: string;
    end_date?: string;
    format?: 'json' | 'csv';
  }): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/logs/export", params);
  }

  // 仪表板数据
  async getDashboardData(): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      return mockAPI.getDashboardData();
    }

    return this.get<any>("/api/v1/dashboard");
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/dashboard/stats");
  }

  async getDashboardCharts(period?: string): Promise<ApiResponse<any>> {
    const params = period ? { period } : {};
    return this.get<any>("/api/v1/dashboard/charts", params);
  }

  // 智能分析
  async getIntelligenceMetrics(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/intelligence/metrics");
  }

  async getIntelligenceInsights(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/intelligence/insights");
  }

  async getIntelligenceRecommendations(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/intelligence/recommendations");
  }

  // 规划和分析
  async createPlan(data: any): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/planning/plans", data);
  }

  async getPlans(): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/planning/plans");
  }

  async getPlan(planId: string): Promise<ApiResponse<any>> {
    return this.get<any>(`/api/v1/planning/plans/${planId}`);
  }

  async updatePlan(planId: string, data: any): Promise<ApiResponse<any>> {
    return this.put<any>(`/api/v1/planning/plans/${planId}`, data);
  }

  async deletePlan(planId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/planning/plans/${planId}`);
  }

  async executePlan(planId: string): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/planning/plans/${planId}/execute`);
  }

  // 系统设置
  async getSystemSettings(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/system/settings");
  }

  async updateSystemSettings(settings: any): Promise<ApiResponse<any>> {
    return this.put<any>("/api/v1/system/settings", settings);
  }

  async getSystemHealth(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/system/health");
  }

  async getSystemInfo(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/system/info");
  }

  // 通知管理
  async getNotifications(): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/notifications");
  }

  async markNotificationAsRead(notificationId: string): Promise<ApiResponse<void>> {
    return this.patch<void>(`/api/v1/notifications/${notificationId}/read`);
  }

  async markAllNotificationsAsRead(): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>("/api/v1/notifications/mark-all-read");
  }

  async deleteNotification(notificationId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/notifications/${notificationId}`);
  }

  // 文件上传
  async uploadFile(file: File, type?: string): Promise<ApiResponse<{ url: string; filename: string }>> {
    const formData = new FormData();
    formData.append("file", file);
    if (type) {
      formData.append("type", type);
    }

    return this.post<{ url: string; filename: string }>("/api/v1/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
  }

  // 搜索
  async globalSearch(query: string, filters?: any): Promise<ApiResponse<any>> {
    const params = { q: query, ...filters };
    return this.get<any>("/api/v1/search", params);
  }

  // 导出数据
  async exportData(type: string, params?: any): Promise<ApiResponse<any>> {
    return this.get<any>(`/api/v1/export/${type}`, params);
  }

  // 导入数据
  async importData(type: string, file: File): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append("file", file);

    return this.post<any>(`/api/v1/import/${type}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
  }

  // 备份和恢复
  async createBackup(): Promise<ApiResponse<{ backup_id: string; message: string }>> {
    return this.post<{ backup_id: string; message: string }>("/api/v1/backup/create");
  }

  async getBackups(): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/backup/list");
  }

  async restoreBackup(backupId: string): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>(`/api/v1/backup/restore/${backupId}`);
  }

  async deleteBackup(backupId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/backup/${backupId}`);
  }

  // 统计和分析
  async getUsageStats(period?: string): Promise<ApiResponse<any>> {
    const params = period ? { period } : {};
    return this.get<any>("/api/v1/stats/usage", params);
  }

  async getPerformanceMetrics(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/stats/performance");
  }

  async getUserActivity(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/stats/user-activity");
  }
}

// 导出单例实例
export const miscAPI = new MiscAPIClient();
