/**
 * Agent-related API endpoints
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import {
  Agent,
  AgentUpdate,
  CreateAgentRequest,
  CreateAgentResponse,
  AgentInvokeRequest,
  AgentInvokeResponse,
  PaginatedResponse,
  AgentFilters,
  PaginationParams,
  ApiResponse
} from "../types";

export class AgentAPIClient extends BaseAPIClient {
  constructor() {
    super();
    // 动态添加方法以避免TypeScript类型冲突
    (this as any).delete = this.deleteById.bind(this);
    (this as any).get = this.getById.bind(this);
  }
  // 获取Agent列表
  async getAgents(
    filters?: AgentFilters,
    pagination?: PaginationParams
  ): Promise<ApiResponse<PaginatedResponse<Agent>>> {
    if (USE_MOCK_API) {
      return mockAPI.getAgents(filters, pagination);
    }

    const params = {
      ...pagination,
      ...filters
    };

    return super.get<PaginatedResponse<Agent>>("/api/v1/agents", params);
  }

  // 获取Agent列表 (别名方法，用于向后兼容)
  async list(
    filters?: AgentFilters,
    pagination?: PaginationParams
  ): Promise<ApiResponse<PaginatedResponse<Agent>>> {
    return this.getAgents(filters, pagination);
  }

  // 获取单个Agent
  async getAgent(id: string): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.getAgent(id);
    }

    return super.get<Agent>(`/api/v1/agents/${id}`);
  }

  // 获取单个Agent (别名方法，用于向后兼容)
  async getById(id: string): Promise<ApiResponse<Agent>> {
    return this.getAgent(id);
  }

  // 创建Agent
  async createAgent(request: CreateAgentRequest): Promise<ApiResponse<CreateAgentResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.createAgent(request);
    }

    return this.post<CreateAgentResponse>("/api/v1/agents", request);
  }

  // 创建Agent (别名方法，用于向后兼容)
  async create(request: CreateAgentRequest): Promise<ApiResponse<CreateAgentResponse>> {
    return this.createAgent(request);
  }

  // 从模板创建Agent
  async createAgentFromTemplate(
    templateId: string,
    customizations?: any
  ): Promise<ApiResponse<CreateAgentResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.createAgentFromTemplate(templateId, customizations);
    }

    return this.post<CreateAgentResponse>("/api/v1/agents/from-template", {
      template_id: templateId,
      customizations: customizations || {}
    });
  }

  // 从模板创建Agent (别名方法，用于向后兼容)
  async createFromTemplate(
    templateId: string,
    customizations?: any
  ): Promise<ApiResponse<CreateAgentResponse>> {
    return this.createAgentFromTemplate(templateId, customizations);
  }

  // 更新Agent
  async updateAgent(id: string, updates: AgentUpdate): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.updateAgent(id, updates);
    }

    return this.put<Agent>(`/api/v1/agents/${id}`, updates);
  }

  // 更新Agent (别名方法，用于向后兼容)
  async update(id: string, updates: AgentUpdate): Promise<ApiResponse<Agent>> {
    return this.updateAgent(id, updates);
  }

  // 删除Agent
  async deleteAgent(id: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAPI.deleteAgent(id);
    }

    // 直接调用父类的delete方法，避免与别名方法冲突
    return super.delete<void>(`/api/v1/agents/${id}`);
  }

  // 删除Agent (别名方法，用于向后兼容)
  async deleteById(id: string): Promise<ApiResponse<void>> {
    return this.deleteAgent(id);
  }

  // 更新Agent状态
  async updateAgentStatus(id: string, status: Agent["status"]): Promise<ApiResponse<Agent>> {
    if (USE_MOCK_API) {
      return mockAPI.updateAgentStatus(id, status);
    }

    // 使用通用的agent更新端点来更新状态
    return this.patch<Agent>(`/api/v1/agents/${id}`, { status });
  }

  // 更新Agent状态 (别名方法，用于向后兼容)
  async updateStatus(id: string, status: Agent["status"]): Promise<ApiResponse<Agent>> {
    return this.updateAgentStatus(id, status);
  }

  // 调用Agent
  async invokeAgent(agentId: string, request: AgentInvokeRequest): Promise<ApiResponse<AgentInvokeResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.invokeAgent(agentId, request);
    }

    return this.post<AgentInvokeResponse>(`/api/v1/agents/${agentId}/execute`, request, {
      timeout: 300000 // 5 minutes timeout for agent execution
    });
  }

  // 流式调用Agent
  async invokeAgentStream(
    agentId: string,
    request: AgentInvokeRequest,
    onMessage: (data: string) => void,
    onError: (error: Error) => void,
    onComplete: () => void
  ): Promise<void> {
    if (USE_MOCK_API) {
      // Mock streaming response
      const mockResponse = await mockAPI.invokeAgent(agentId, request);
      if (mockResponse.success && mockResponse.data) {
        onMessage(mockResponse.data.output);
        onComplete();
      } else {
        onError(new Error(mockResponse.error?.message || "Mock API error"));
      }
      return;
    }

    try {
      const response = await fetch(`${this.axios.defaults.baseURL}/api/v1/agents/${agentId}/execute/stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("auth_token")}`
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              onMessage(data.content || data.message || line);
            } catch {
              onMessage(line);
            }
          }
        }
      }

      onComplete();
    } catch (error) {
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // 带进度的流式调用Agent
  async invokeAgentStreamWithProgress(
    agentId: string,
    request: AgentInvokeRequest,
    callbacks: {
      onProgress?: (progressData: any) => void;
      onComplete?: (result: any) => void;
      onError?: (error: string) => void;
    }
  ): Promise<void> {
    if (USE_MOCK_API) {
      // Mock streaming with progress
      const mockResponse = await mockAPI.invokeAgent(agentId, request);
      if (mockResponse.success && mockResponse.data) {
        // Simulate progress updates
        callbacks.onProgress?.({ stage: "processing", progress: 50 });
        setTimeout(() => {
          callbacks.onProgress?.({ stage: "completing", progress: 100 });
          callbacks.onComplete?.(mockResponse.data);
        }, 1000);
      } else {
        callbacks.onError?.(mockResponse.error?.message || "Mock API error");
      }
      return;
    }

    try {
      const response = await fetch(`${this.axios.defaults.baseURL}/api/v1/agents/${agentId}/execute/stream-progress`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("auth_token")}`
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.type === "progress") {
                callbacks.onProgress?.(data.data);
              } else if (data.type === "complete") {
                callbacks.onComplete?.(data.data);
              } else if (data.type === "error") {
                callbacks.onError?.(data.message);
              }
            } catch (parseError) {
              console.warn("Failed to parse streaming data:", line);
            }
          }
        }
      }
    } catch (error) {
      callbacks.onError?.(error instanceof Error ? error.message : String(error));
    }
  }

  // 重启Agent
  async restartAgent(agentId: string): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>(`/api/v1/agents/${agentId}/restart`);
  }

  // 切换Agent收藏状态
  async toggleAgentFavorite(agentId: string): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/agents/${agentId}/favorite`);
  }

  // 获取收藏的Agent列表
  async getFavoriteAgents(includePerformance: boolean = true): Promise<ApiResponse<any[]>> {
    return super.get<any[]>(`/api/v1/agents/favorites?include_performance=${includePerformance}`);
  }

  // 发现Agent变量
  async discoverAgentVariables(agentId: string): Promise<ApiResponse<any>> {
    return super.get<any>(`/api/v1/agents/${agentId}/variables`);
  }

  // 发现Agent变量 (别名方法，用于向后兼容)
  async discoverVariables(agentId: string): Promise<ApiResponse<any>> {
    return this.discoverAgentVariables(agentId);
  }
}

// 导出单例实例
export const agentAPI = new AgentAPIClient();
