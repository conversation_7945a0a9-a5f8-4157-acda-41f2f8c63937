/**
 * Authentication related API endpoints
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import { ApiResponse } from "../types";

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  confirm_password: string;
}

export interface LoginResponse {
  user: {
    id: number;
    uuid: string;
    name: string;
    email: string;
    role: string;
    status: string;
    avatar?: string;
    bio?: string;
    timezone?: string;
    language?: string;
    last_login_at?: string;
    is_email_verified: boolean;
    login_count: number;
    is_2fa_enabled?: boolean;
    two_fa_enabled_at?: string;
    created_at: string;
    updated_at: string;
  };
  tokens: {
    access_token: string;
    token_type: string;
    expires_in: number;
  };
  session_id: string;
}

export interface UserProfile {
  id: number;
  uuid: string;
  name: string;
  email: string;
  role: string;
  status: string;
  avatar?: string;
  bio?: string;
  timezone?: string;
  language?: string;
  last_login_at?: string;
  is_email_verified: boolean;
  login_count: number;
  is_2fa_enabled?: boolean;
  two_fa_enabled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserUpdate {
  name?: string;
  bio?: string;
  timezone?: string;
  language?: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ResetPasswordConfirmRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

export class AuthAPIClient extends BaseAPIClient {
  // 用户注册
  async register(request: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.register(request);
    }

    return this.post<LoginResponse>("/api/v1/auth/register", request);
  }

  // 用户登录
  async login(request: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.login(request);
    }

    return this.post<LoginResponse>("/api/v1/auth/login", request);
  }

  // 用户登出
  async logout(): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.logout();
    }

    return this.post<{ message: string }>("/api/v1/auth/logout");
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<ApiResponse<UserProfile>> {
    if (USE_MOCK_API) {
      return mockAPI.getCurrentUser();
    }

    return this.get<UserProfile>("/api/v1/auth/me");
  }

  // 更新用户资料
  async updateProfile(updates: UserUpdate): Promise<ApiResponse<UserProfile>> {
    if (USE_MOCK_API) {
      return mockAPI.updateProfile(updates);
    }

    return this.put<UserProfile>("/api/v1/auth/me", updates);
  }

  // 修改密码
  async changePassword(request: ChangePasswordRequest): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.changePassword(request);
    }

    return this.put<{ message: string }>("/api/v1/auth/change-password", request);
  }

  // 请求密码重置
  async requestPasswordReset(request: ResetPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.requestPasswordReset(request);
    }

    return this.post<{ message: string }>("/api/v1/auth/reset-password", request);
  }

  // 确认密码重置
  async confirmPasswordReset(request: ResetPasswordConfirmRequest): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.confirmPasswordReset(request);
    }

    return this.post<{ message: string }>("/api/v1/auth/reset-password/confirm", request);
  }

  // 上传头像
  async uploadAvatar(file: File): Promise<ApiResponse<{ avatar_url: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.uploadAvatar(file);
    }

    const formData = new FormData();
    formData.append("file", file);

    return this.post<{ avatar_url: string }>("/api/v1/auth/upload-avatar", formData, {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
  }

  // 获取用户会话列表
  async getUserSessions(): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/auth/sessions");
  }

  // 撤销用户会话
  async revokeSession(sessionUuid: string): Promise<ApiResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`/api/v1/auth/sessions/${sessionUuid}`);
  }

  // 撤销所有会话
  async revokeAllSessions(): Promise<ApiResponse<{ message: string; revoked_count: number }>> {
    return this.delete<{ message: string; revoked_count: number }>("/api/v1/auth/sessions");
  }

  // 获取登录历史
  async getLoginHistory(limit: number = 50): Promise<ApiResponse<any[]>> {
    return this.get<any[]>("/api/v1/auth/login-history", { limit });
  }

  // 2FA相关方法
  async enable2FA(): Promise<ApiResponse<{ qr_code: string; secret: string; backup_codes: string[] }>> {
    return this.post<{ qr_code: string; secret: string; backup_codes: string[] }>("/api/v1/auth/2fa/enable");
  }

  async verify2FASetup(totp_code: string): Promise<ApiResponse<{ message: string; backup_codes: string[] }>> {
    return this.post<{ message: string; backup_codes: string[] }>("/api/v1/auth/2fa/verify-setup", {
      totp_code
    });
  }

  async disable2FA(totp_code?: string, backup_code?: string): Promise<ApiResponse<{ message: string }>> {
    return this.post<{ message: string }>("/api/v1/auth/2fa/disable", {
      totp_code,
      backup_code
    });
  }

  async regenerateBackupCodes(totp_code?: string, backup_code?: string): Promise<ApiResponse<{ backup_codes: string[] }>> {
    return this.post<{ backup_codes: string[] }>("/api/v1/auth/2fa/regenerate-backup-codes", {
      totp_code,
      backup_code
    });
  }

  async verify2FA(data: {
    temp_session_id: string;
    totp_code?: string;
    backup_code?: string;
    remember_me?: boolean;
  }): Promise<ApiResponse<LoginResponse>> {
    return this.post<LoginResponse>("/api/v1/auth/verify-2fa", data);
  }

  // 验证token有效性
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user?: UserProfile }>> {
    try {
      const response = await this.getCurrentUser();
      if (response.success) {
        return {
          success: true,
          data: { valid: true, user: response.data },
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          success: true,
          data: { valid: false },
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      return {
        success: true,
        data: { valid: false },
        timestamp: new Date().toISOString()
      };
    }
  }

  // 刷新token
  async refreshToken(): Promise<ApiResponse<{ access_token: string; expires_in: number }>> {
    return this.post<{ access_token: string; expires_in: number }>("/api/v1/auth/refresh");
  }
}

// 导出单例实例
export const authAPI = new AuthAPIClient();
