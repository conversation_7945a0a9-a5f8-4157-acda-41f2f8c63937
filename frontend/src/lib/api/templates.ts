/**
 * Template-related API endpoints
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import {
  Template,
  TemplateListItem,
  TemplateFilters,
  TemplateCreateRequest,
  TemplateUpdateRequest,
  TemplateFromAgentRequest,
  TemplateCategory,
  TemplateDifficulty,
  TemplateVisibility,
  TemplateStats,
  PopularTag,
  PaginatedTemplateResponse,
  ApiResponse
} from "../types";

export class TemplateAPIClient extends BaseAPIClient {
  // 获取模板列表
  async getTemplates(
    filters?: TemplateFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedTemplateResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplates(filters, page, limit);
    }

    const params = {
      page,
      limit,
      ...filters
    };

    return this.get<PaginatedTemplateResponse>("/api/v1/templates", params);
  }

  // 获取模板列表 (分页版本，别名方法)
  async listPaginated(
    filters?: TemplateFilters & { page?: number; limit?: number; sort_by?: string; sort_order?: string }
  ): Promise<ApiResponse<PaginatedTemplateResponse>> {
    const { page = 1, limit = 20, sort_by, sort_order, ...otherFilters } = filters || {};
    return this.getTemplates(otherFilters, page, limit);
  }

  // 获取模板列表 (别名方法)
  async list(
    filters?: TemplateFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedTemplateResponse>> {
    return this.getTemplates(filters, page, limit);
  }

  // 获取单个模板
  async getTemplate(id: string): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplate(id);
    }

    return this.get<Template>(`/api/v1/templates/${id}`);
  }

  // 获取单个模板 (别名方法)
  async getById(id: string): Promise<ApiResponse<Template>> {
    return this.getTemplate(id);
  }

  // 创建模板
  async createTemplate(request: TemplateCreateRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.createTemplate(request);
    }

    return this.post<Template>("/api/v1/templates", request);
  }

  // 创建模板 (别名方法，用于向后兼容)
  async create(request: TemplateCreateRequest): Promise<ApiResponse<Template>> {
    return this.createTemplate(request);
  }

  // 从Agent创建模板
  async createTemplateFromAgent(request: TemplateFromAgentRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.createTemplateFromAgent(request);
    }

    return this.post<Template>("/api/v1/templates/from-agent", request);
  }

  // 从Agent创建模板 (别名方法，用于向后兼容)
  async createFromAgent(request: TemplateFromAgentRequest): Promise<ApiResponse<Template>> {
    return this.createTemplateFromAgent(request);
  }

  // 更新模板
  async updateTemplate(id: string, updates: TemplateUpdateRequest): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.updateTemplate(id, updates);
    }

    return this.put<Template>(`/api/v1/templates/${id}`, updates);
  }

  // 删除模板
  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAPI.deleteTemplate(id);
    }

    return this.delete<void>(`/api/v1/templates/${id}`);
  }

  // 搜索模板
  async searchTemplates(
    query: string,
    filters?: Omit<TemplateFilters, 'search'>
  ): Promise<ApiResponse<PaginatedTemplateResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.searchTemplates(query, filters);
    }

    const params = {
      search: query,
      ...filters
    };

    return this.get<PaginatedTemplateResponse>("/api/v1/templates/search", params);
  }

  // 获取模板版本
  async getTemplateVersions(templateId: string): Promise<ApiResponse<Template[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplateVersions(templateId);
    }

    return this.get<Template[]>(`/api/v1/templates/${templateId}/versions`);
  }

  // 获取模板版本 (别名方法)
  async getVersions(templateId: string): Promise<ApiResponse<Template[]>> {
    return this.getTemplateVersions(templateId);
  }

  // 创建模板版本
  async createTemplateVersion(
    templateId: string,
    updates: TemplateUpdateRequest,
    version?: string
  ): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.createTemplateVersion(templateId, updates, version);
    }

    return this.post<Template>(`/api/v1/templates/${templateId}/versions`, {
      ...updates,
      version
    });
  }

  // 回滚模板
  async rollbackTemplate(
    templateId: string,
    versionTemplateId: string
  ): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.rollbackTemplate(templateId, versionTemplateId);
    }

    return this.post<Template>(`/api/v1/templates/${templateId}/rollback`, {
      version_template_id: versionTemplateId
    });
  }

  // 获取模板分类
  async getTemplateCategories(): Promise<ApiResponse<TemplateCategory[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplateCategories();
    }

    return this.get<TemplateCategory[]>("/api/v1/templates/categories/list");
  }

  // 获取模板分类 (别名方法)
  async getCategories(): Promise<ApiResponse<TemplateCategory[]>> {
    return this.getTemplateCategories();
  }

  // 获取模板难度级别
  async getTemplateDifficulties(): Promise<ApiResponse<TemplateDifficulty[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplateDifficulties();
    }

    return this.get<TemplateDifficulty[]>("/api/v1/templates/difficulties/list");
  }

  // 获取模板难度级别 (别名方法)
  async getDifficulties(): Promise<ApiResponse<TemplateDifficulty[]>> {
    return this.getTemplateDifficulties();
  }

  // 获取热门标签
  async getPopularTags(limit?: number): Promise<ApiResponse<PopularTag[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getPopularTags(limit);
    }

    const params = limit ? { limit } : {};
    return this.get<PopularTag[]>("/api/v1/templates/tags/popular", params);
  }

  // 获取模板统计
  async getTemplateStats(): Promise<ApiResponse<TemplateStats>> {
    if (USE_MOCK_API) {
      return mockAPI.getTemplateStats();
    }

    return this.get<TemplateStats>("/api/v1/templates/stats");
  }

  // 分享模板
  async shareTemplate(
    templateId: string,
    visibility: TemplateVisibility
  ): Promise<ApiResponse<Template>> {
    if (USE_MOCK_API) {
      return mockAPI.shareTemplate(templateId, visibility);
    }

    return this.post<Template>(`/api/v1/templates/${templateId}/share`, {
      visibility
    });
  }

  // 获取精选模板
  async getFeaturedTemplates(limit?: number): Promise<ApiResponse<TemplateListItem[]>> {
    if (USE_MOCK_API) {
      return mockAPI.getFeaturedTemplates(limit);
    }

    const params = limit ? { limit } : {};
    return this.get<TemplateListItem[]>("/api/v1/templates/featured", params);
  }

  // 获取社区模板
  async getCommunityTemplates(filters?: any): Promise<ApiResponse<PaginatedTemplateResponse>> {
    if (USE_MOCK_API) {
      return mockAPI.getCommunityTemplates(filters);
    }

    return this.get<PaginatedTemplateResponse>("/api/v1/templates/community", filters);
  }

  // 获取社区模板 (别名方法)
  async getCommunity(filters?: any): Promise<ApiResponse<PaginatedTemplateResponse>> {
    return this.getCommunityTemplates(filters);
  }

  // 评价模板
  async rateTemplate(templateId: string, rating: number): Promise<ApiResponse<{ message: string }>> {
    if (USE_MOCK_API) {
      return mockAPI.rateTemplate(templateId, rating);
    }

    return this.post<{ message: string }>(`/api/v1/templates/${templateId}/rate`, {
      rating
    });
  }

  // 复制模板
  async duplicateTemplate(templateId: string, name?: string): Promise<ApiResponse<Template>> {
    return this.post<Template>(`/api/v1/templates/${templateId}/duplicate`, {
      name
    });
  }

  // 导出模板
  async exportTemplate(templateId: string, format: 'json' | 'yaml' = 'json'): Promise<ApiResponse<any>> {
    return this.get<any>(`/api/v1/templates/${templateId}/export?format=${format}`);
  }

  // 导入模板
  async importTemplate(templateData: any): Promise<ApiResponse<Template>> {
    return this.post<Template>("/api/v1/templates/import", templateData);
  }

  // 验证模板
  async validateTemplate(templateData: any): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return this.post<{ valid: boolean; errors?: string[] }>("/api/v1/templates/validate", templateData);
  }
}

// 导出单例实例
export const templateAPI = new TemplateAPIClient();
