/**
 * Planning-related API endpoints
 */

import { BaseAPIClient, USE_MOCK_API } from "./base";
import { mockAPI } from "../mock-api";
import { ApiResponse } from "../types";

export class PlanningAPIClient extends BaseAPIClient {
  // Analyze requirements and generate team plan
  async analyze(userDescription: string): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      // Simulate async response for mock
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        data: {
          request_id: "mock_plan_123",
          status: "completed",
          team_plan: {
            team_name: "测试团队",
            description: "基于用户需求生成的测试团队",
            objective: "为用户提供智能服务",
            team_members: [
              {
                name: "分析师",
                role: "数据分析专家",
                system_prompt: "你是一个专业的数据分析师...",
                description: "负责数据分析和洞察"
              }
            ],
            workflow: {
              steps: [
                {
                  name: "需求分析",
                  description: "分析用户需求",
                  assignee: "分析师",
                  inputs: ["用户描述"],
                  outputs: ["需求文档"]
                }
              ]
            }
          },
          message: "Team plan generated successfully"
        },
        timestamp: new Date().toISOString()
      };
    }

    // Start async team generation
    const response = await this.post<any>("/api/v1/planning/analyze", {
      user_description: userDescription
    });

    if (!response.success || !response.data?.request_id) {
      return response;
    }

    // Poll for completion
    return this.pollPlanningStatus(response.data.request_id);
  }

  // Create planning request
  async create(userDescription: string, templateId?: string): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      // Simulate async response for mock
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        data: {
          request_id: "mock_plan_456",
          status: "completed",
          team_plan: {
            team_name: "测试团队",
            description: "基于用户需求生成的测试团队",
            objective: "为用户提供智能服务",
            team_members: [],
            workflow: { steps: [] }
          },
          message: "Team plan generated using ai_powered method"
        },
        timestamp: new Date().toISOString()
      };
    }

    const data: any = { user_description: userDescription };
    if (templateId) {
      data.template_id = templateId;
    }

    // Start async team generation
    const response = await this.post<any>("/api/v1/planning/generate", data);

    if (!response.success || !response.data?.request_id) {
      return response;
    }

    // Poll for completion
    return this.pollPlanningStatus(response.data.request_id);
  }

  // Poll planning status
  async pollPlanningStatus(requestId: string, maxAttempts: number = 30): Promise<ApiResponse<any>> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await this.get<any>(`/api/v1/planning/status/${requestId}`);

        if (!response.success) {
          return response;
        }

        const status = response.data?.status;
        
        if (status === "completed") {
          return response;
        } else if (status === "failed") {
          return {
            success: false,
            error: {
              code: "PLANNING_FAILED",
              message: response.data?.error || "Planning failed"
            },
            timestamp: new Date().toISOString()
          };
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`Polling attempt ${attempt + 1} failed:`, error);
        
        if (attempt === maxAttempts - 1) {
          return {
            success: false,
            error: {
              code: "POLLING_TIMEOUT",
              message: "Planning request timed out"
            },
            timestamp: new Date().toISOString()
          };
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return {
      success: false,
      error: {
        code: "POLLING_TIMEOUT",
        message: "Planning request timed out"
      },
      timestamp: new Date().toISOString()
    };
  }

  // Get planning status
  async getStatus(requestId: string): Promise<ApiResponse<any>> {
    return this.get<any>(`/api/v1/planning/status/${requestId}`);
  }

  // Validate team plan
  async validatePlan(teamPlan: any): Promise<ApiResponse<any>> {
    return this.post<any>("/api/v1/planning/validate", { team_plan: teamPlan });
  }

  // Submit feedback
  async submitFeedback(requestId: string, feedback: {
    rating: number;
    comments?: string;
    improvements?: string[];
  }): Promise<ApiResponse<any>> {
    return this.post<any>(`/api/v1/planning/feedback/${requestId}`, feedback);
  }

  // Get planning history
  async getHistory(limit?: number): Promise<ApiResponse<any[]>> {
    const params = limit ? { limit } : {};
    return this.get<any[]>("/api/v1/planning/history", params);
  }

  // Delete planning request
  async deletePlanningRequest(requestId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/api/v1/planning/${requestId}`);
  }

  // Get planning statistics
  async getStatistics(): Promise<ApiResponse<any>> {
    return this.get<any>("/api/v1/planning/stats");
  }
}

// Export singleton instance
export const planningAPI = new PlanningAPIClient();
