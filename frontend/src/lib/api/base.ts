/**
 * Base API client configuration and utilities
 */

import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import { ApiResponse } from "../types";
import { handleApiError, handleNetworkError, handleTimeoutError, handleAuthError, handleServerError } from "../error-handler";

// API配置
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
export const USE_MOCK_API = process.env.NEXT_PUBLIC_MOCK_API === "true";

// 创建axios实例
export const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000, // Default timeout for most requests
    headers: {
      "Content-Type": "application/json",
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem("auth_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 记录请求日志
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });

      return config;
    },
    (error) => {
      console.error("[API Request Error]", error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 记录响应日志
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });

      return response;
    },
    (error) => {
      console.error("[API Response Error]", error);

      // 使用标准化错误处理
      if (error.response) {
        const { status } = error.response;
        switch (status) {
          case 401:
            handleAuthError(error, {
              onError: () => {
                localStorage.removeItem("auth_token");
                window.location.href = "/login";
              }
            });
            break;
          case 403:
            handleApiError(error, { fallbackMessage: "权限不足" });
            break;
          case 404:
            handleApiError(error, { fallbackMessage: "资源不存在" });
            break;
          case 429:
            handleApiError(error, { fallbackMessage: "请求过于频繁，请稍后再试" });
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            handleServerError(error);
            break;
          default:
            handleApiError(error);
        }
      } else if (error.request) {
        // 网络错误
        handleNetworkError(error);
      } else if (error.code === 'ECONNABORTED') {
        // 超时错误
        handleTimeoutError();
      } else {
        // 其他错误
        handleApiError(error);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 基础API客户端类
export class BaseAPIClient {
  protected axios: AxiosInstance;

  constructor() {
    this.axios = createAxiosInstance();
  }

  // 通用请求方法
  protected async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.axios.request<T>(config);
      // 后端直接返回数据，我们包装成ApiResponse格式
      return {
        success: true,
        data: response.data,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      // 使用标准化错误处理
      const handledError = handleApiError(error, {
        logError: true,
        showToast: false // 让调用方决定是否显示toast
      });

      const apiError: ApiResponse<T> = {
        success: false,
        error: {
          code: handledError.code || "UNKNOWN_ERROR",
          message: handledError.message,
          details: handledError.details
        },
        timestamp: new Date().toISOString()
      };
      return apiError;
    }
  }

  // GET请求
  protected async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: "GET",
      url,
      params
    });
  }

  // POST请求
  protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: "POST",
      url,
      data,
      ...config
    });
  }

  // PUT请求
  protected async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: "PUT",
      url,
      data
    });
  }

  // DELETE请求
  protected async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: "DELETE",
      url
    });
  }

  // PATCH请求
  protected async patch<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: "PATCH",
      url,
      data
    });
  }

  // 健康检查
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.get<{ status: string; timestamp: string }>("/api/v1/health");
  }
}
