"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/main-layout";
import { TemplateForm } from "@/components/templates/TemplateForm";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { 
  Template,
  TemplateUpdateRequest
} from "@/lib/types";

export default function EditTemplatePage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  
  const templateId = params.templateId as string;
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [difficulties, setDifficulties] = useState<Array<{ value: string; label: string }>>([]);

  // Load template and metadata
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const [templateRes, categoriesRes, difficultiesRes] = await Promise.all([
          api.templates.getTemplate(templateId),
          api.templates.getCategories(),
          api.templates.getDifficulties()
        ]);
        
        if (templateRes.success && templateRes.data) {
          setTemplate(templateRes.data);
          
          // Check if user can edit this template
          if (!templateRes.data.can_edit) {
            throw new Error("您没有权限编辑此模板");
          }
        } else {
          throw new Error(templateRes.error?.message || "Failed to load template");
        }
        
        if (categoriesRes.success && categoriesRes.data) {
          setCategories(categoriesRes.data);
        }
        
        if (difficultiesRes.success && difficultiesRes.data) {
          setDifficulties(difficultiesRes.data);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load template";
        setError(errorMessage);
        toast({
          title: "加载失败",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (templateId) {
      loadData();
    }
  }, [templateId, toast]);

  const handleSubmit = async (data: TemplateUpdateRequest) => {
    try {
      setSaving(true);
      setError(null);
      
      const response = await api.templates.update(templateId, data);
      
      if (response.success && response.data) {
        toast({
          title: "保存成功",
          description: "模板已成功更新",
        });
        
        // Navigate back to template view
        router.push(`/templates/${templateId}`);
      } else {
        throw new Error(response.error?.message || "Failed to update template");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update template";
      setError(errorMessage);
      toast({
        title: "保存失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">正在加载模板...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error && !template) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <Alert variant="destructive">
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  if (!template) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold mb-2">模板未找到</h3>
          <p className="text-muted-foreground">
            请检查模板ID是否正确，或者您是否有权限访问此模板。
          </p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">编辑模板</h1>
          <p className="text-muted-foreground mt-1">
            编辑模板 "{template.name}" 的配置和内容
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Template Form */}
        <TemplateForm
          mode="edit"
          template={template}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={saving}
          categories={categories}
          difficulties={difficulties}
          onToast={toast}
        />
      </div>
    </MainLayout>
  );
}
