"""
Config-driven agent runtime - Executes agents based on configuration without code generation.
"""

import uuid
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.core.logging import get_logger
from app.core.timezone_utils import utc_now
from app.services.context_service import get_context_service
from app.models.context import (
    ContextAwareTeamMember,
    ContextAwareWorkflowStep,
    ensure_context_compatibility
)

logger = get_logger(__name__)


class ConfigDrivenAgent:
    """基于配置的Agent运行时 - 无需代码生成"""

    def __init__(self, agent_config: Dict[str, Any]):
        self.config = agent_config
        self.team_plan = agent_config.get("team_plan", {})

        # Ensure context compatibility for existing team plans
        self.team_plan = ensure_context_compatibility(self.team_plan)

        self.team_members = self.team_plan.get("team_members", [])
        self.workflow = self.team_plan.get("workflow", {})
        self.agent_id = agent_config.get("agent_id")
        self.team_name = agent_config.get("team_name", "Unknown Team")

        # Migrate workflow assignees from name to role if needed
        self._migrate_workflow_assignees()

        # Initialize user context (will be set by the execution framework)
        self.current_user = None
        self.test_id = None
        self.execution_id = None

        # Initialize context service
        self.context_service = get_context_service()

        logger.info(f"ConfigDrivenAgent initialized: {self.team_name} ({self.agent_id})")

    def _migrate_workflow_assignees(self):
        """Migrate workflow assignees from name-based to role-based format."""
        if not self.workflow or "steps" not in self.workflow:
            return

        # Build name to role mapping
        name_to_role = {}
        for member in self.team_members:
            if isinstance(member, dict):
                name = member.get("name")
                role = member.get("role")
                if name and role:
                    name_to_role[name] = role

        # Migrate each workflow step
        migrated_count = 0
        for step in self.workflow["steps"]:
            assignee = step.get("assignee")
            if assignee and assignee in name_to_role:
                old_assignee = assignee
                new_assignee = name_to_role[assignee]
                step["assignee"] = new_assignee
                migrated_count += 1
                logger.info(f"Migrated workflow assignee: '{old_assignee}' -> '{new_assignee}'")
            elif assignee == "团队协作":
                # Migrate Chinese team collaboration to English
                step["assignee"] = "team_collaboration"
                migrated_count += 1
                logger.info(f"Migrated team collaboration: '团队协作' -> 'team_collaboration'")

        if migrated_count > 0:
            logger.info(f"Workflow migration completed: {migrated_count} assignees migrated to role-based format")

    def set_test_id(self, test_id: str):
        """Set test ID for variable tracking integration."""
        self.test_id = test_id
        logger.debug(f"Test ID set for agent {self.agent_id}: {test_id}")

    def set_current_user(self, user):
        """Set current user for execution context."""
        self.current_user = user
        logger.debug(f"Current user set for agent {self.agent_id}: {user.id if user else None}")

    async def execute(self, input_data: Dict, progress_callback=None) -> Dict:
        """执行基于配置的Agent工作流"""
        try:
            logger.info(f"开始执行配置驱动Agent: {self.team_name}")

            # Initialize execution context for this run
            self.execution_id = f"exec_{uuid.uuid4().hex[:12]}"
            context_manager = self.context_service.create_execution_context(self.execution_id)

            # Add user input to context
            user_input = input_data.get("input", "")
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name="user_input",
                member_name="system",
                context_data={"user_input": user_input, "output": user_input}
            )

            # Initialize variable tracker if test_id is set
            if self.test_id:
                from app.services.variable_tracker import VariableTracker
                self.variable_tracker = VariableTracker(self.test_id)
                await self.variable_tracker.initialize()

            # Execute workflow steps
            workflow_results = []
            if self.workflow and "steps" in self.workflow:
                for i, step in enumerate(self.workflow["steps"]):
                    step_result = await self._execute_workflow_step(step, i, progress_callback)
                    workflow_results.append(step_result)

                    # Report progress
                    if progress_callback:
                        progress = {
                            "step": i + 1,
                            "total_steps": len(self.workflow["steps"]),
                            "step_name": step.get("name", f"Step {i + 1}"),
                            "status": "completed",
                            "result": step_result
                        }
                        await progress_callback(progress)

            # Finalize execution
            final_result = self._finalize_execution_result(workflow_results, input_data)

            # Update variables in database if tracking is enabled
            if hasattr(self, 'variable_tracker'):
                await self._update_variables_in_database()

            logger.info(f"配置驱动Agent执行完成: {self.team_name}")
            return final_result

        except Exception as e:
            logger.error(f"配置驱动Agent执行失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "agent_id": self.agent_id,
                "team_name": self.team_name,
                "executed_at": utc_now().isoformat()
            }

    async def _execute_workflow_step(self, step: Dict, step_index: int, progress_callback=None) -> Dict:
        """Execute a single workflow step."""
        step_name = step.get("name", f"Step {step_index + 1}")
        assignee = step.get("assignee", "")
        description = step.get("description", "")

        logger.info(f"执行工作流步骤: {step_name} (分配给: {assignee})")

        try:
            # Find the team member responsible for this step
            team_member = self._find_team_member(assignee)
            if not team_member:
                logger.warning(f"未找到负责人: {assignee}, 使用默认成员")
                team_member = self.team_members[0] if self.team_members else {
                    "name": "Default Member",
                    "role": "default",
                    "description": "Default team member"
                }

            # Get context for this step
            context = self.context_service.get_step_context(
                execution_id=self.execution_id,
                step_name=step_name
            )

            # Execute the step using AI
            step_result = await self._execute_step_with_ai(
                step, team_member, context, step_index
            )

            # Add step result to context
            self.context_service.add_step_context(
                execution_id=self.execution_id,
                step_name=step_name,
                member_name=team_member.get("name", "Unknown"),
                context_data=step_result
            )

            return step_result

        except Exception as e:
            logger.error(f"工作流步骤执行失败 {step_name}: {str(e)}")
            return {
                "step_name": step_name,
                "assignee": assignee,
                "status": "error",
                "error": str(e),
                "executed_at": utc_now().isoformat()
            }

    async def _execute_step_with_ai(self, step: Dict, team_member: Dict, context: Dict, step_index: int) -> Dict:
        """Execute step using AI provider."""
        from app.services.ai_providers import get_ai_provider

        step_name = step.get("name", f"Step {step_index + 1}")
        description = step.get("description", "")
        
        # Build prompt for this step
        prompt = self._build_step_prompt(step, team_member, context)

        # Get AI configuration
        ai_config = self.config.get("ai_config", {})
        provider = ai_config.get("provider", "openai")
        model = ai_config.get("model", "gpt-4")
        temperature = ai_config.get("temperature", 0.7)

        try:
            # Get AI provider and execute
            ai_provider = get_ai_provider(provider)
            ai_response = await ai_provider.generate_response(
                prompt=prompt,
                model=model,
                temperature=temperature,
                max_tokens=ai_config.get("max_tokens", 2000)
            )

            # Track variables if enabled
            if hasattr(self, 'variable_tracker'):
                await self._track_step_variables(step, ai_response, step_index)

            result = {
                "step_name": step_name,
                "assignee": team_member.get("role", "unknown"),
                "member_name": team_member.get("name", "Unknown"),
                "description": description,
                "output": ai_response,
                "status": "completed",
                "executed_at": utc_now().isoformat(),
                "ai_config": {
                    "provider": provider,
                    "model": model,
                    "temperature": temperature
                }
            }

            logger.info(f"步骤执行成功: {step_name}")
            return result

        except Exception as e:
            logger.error(f"AI执行步骤失败 {step_name}: {str(e)}")
            return {
                "step_name": step_name,
                "assignee": team_member.get("role", "unknown"),
                "status": "error",
                "error": str(e),
                "executed_at": utc_now().isoformat()
            }

    def _build_step_prompt(self, step: Dict, team_member: Dict, context: Dict) -> str:
        """Build prompt for workflow step execution."""
        step_name = step.get("name", "")
        description = step.get("description", "")
        member_name = team_member.get("name", "Team Member")
        member_role = team_member.get("role", "")
        member_description = team_member.get("description", "")

        # Get previous context
        previous_outputs = []
        if context and "previous_steps" in context:
            for prev_step in context["previous_steps"]:
                if "output" in prev_step:
                    previous_outputs.append(f"- {prev_step.get('step_name', 'Previous Step')}: {prev_step['output']}")

        context_text = "\n".join(previous_outputs) if previous_outputs else "No previous context available."

        prompt = f"""You are {member_name}, a {member_role} in our team.

Your role description: {member_description}

Current task: {step_name}
Task description: {description}

Previous work context:
{context_text}

Please complete this task according to your role and expertise. Provide a clear, actionable output that the next team member can build upon.

Your response:"""

        return prompt

    def _find_team_member(self, role: str) -> Optional[Dict]:
        """查找团队成员 - 只支持通过role查找"""
        # Handle special case for team collaboration
        if role == "team_collaboration":
            # For collaborative steps, use the first team member as the primary executor
            # but mark it as a collaborative step
            if self.team_members:
                primary_member = self.team_members[0].copy()
                primary_member["is_collaborative"] = True
                primary_member["collaboration_type"] = "team_collaboration"
                return primary_member
            else:
                # Fallback if no team members exist
                return {
                    "name": "团队协作",
                    "role": "team_collaboration",
                    "description": "团队协作执行",
                    "is_collaborative": True,
                    "collaboration_type": "team_collaboration"
                }

        # Find by role only
        for member in self.team_members:
            if member.get("role") == role:
                return member

        # If not found by role, return None
        logger.warning(f"未找到角色为 '{role}' 的团队成员")
        return None

    def _finalize_execution_result(self, workflow_results: List[Dict], input_data: Dict) -> Dict:
        """Finalize and format the execution result."""
        # Get the final output from the last successful step
        final_output = ""
        if workflow_results:
            for result in reversed(workflow_results):
                if result.get("status") == "completed" and result.get("output"):
                    final_output = result["output"]
                    break

        return {
            "status": "completed",
            "agent_id": self.agent_id,
            "team_name": self.team_name,
            "input": input_data.get("input", ""),
            "output": final_output,
            "workflow_results": workflow_results,
            "execution_id": self.execution_id,
            "executed_at": utc_now().isoformat(),
            "total_steps": len(workflow_results),
            "successful_steps": len([r for r in workflow_results if r.get("status") == "completed"])
        }

    async def _track_step_variables(self, step: Dict, ai_response: str, step_index: int):
        """Track variables for this step if variable tracking is enabled."""
        if not hasattr(self, 'variable_tracker'):
            return

        try:
            # Extract variables from step configuration
            variables = step.get("variables", [])
            if not variables:
                return

            for var_config in variables:
                var_name = var_config.get("name")
                var_type = var_config.get("type", "text")
                
                if not var_name:
                    continue

                # Extract variable value from AI response
                var_value = self._extract_variable_value(ai_response, var_config)
                
                # Track the variable
                await self.variable_tracker.track_variable(
                    variable_name=var_name,
                    value=var_value,
                    step_index=step_index,
                    step_name=step.get("name", f"Step {step_index + 1}"),
                    variable_type=var_type,
                    source="ai_response"
                )

        except Exception as e:
            logger.error(f"Variable tracking failed for step {step_index}: {str(e)}")

    def _extract_variable_value(self, ai_response: str, var_config: Dict) -> str:
        """Extract variable value from AI response based on configuration."""
        var_type = var_config.get("type", "text")
        extraction_method = var_config.get("extraction_method", "full_response")

        if extraction_method == "full_response":
            return ai_response.strip()
        elif extraction_method == "pattern":
            return self._extract_by_pattern(ai_response, var_config.get("pattern", ""))
        elif extraction_method == "description":
            return self._extract_by_description(ai_response, var_config.get("description", ""))
        else:
            return ai_response.strip()

    def _extract_by_pattern(self, ai_response: str, pattern: str) -> str:
        """Extract variable value using pattern matching."""
        import re
        
        if not pattern:
            return ai_response.strip()

        try:
            match = re.search(pattern, ai_response, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1) if match.groups() else match.group(0)
        except Exception as e:
            logger.warning(f"Pattern extraction failed: {str(e)}")

        return ai_response.strip()

    def _extract_by_description(self, ai_response: str, description: str) -> str:
        """Extract variable value based on semantic description."""
        # For now, return the full response
        # TODO: Implement description-based extraction using NLP
        return ai_response.strip()

    async def _update_variables_in_database(self):
        """Update the test_history database with variable data from VariableTracker."""
        if not hasattr(self, 'variable_tracker'):
            return

        try:
            # Get all tracked variables
            variables = self.variable_tracker.get_all_variables()
            
            if not variables:
                return

            # Update test history with variable data
            from app.services.test_history_service import TestHistoryService
            
            test_history_service = TestHistoryService()
            await test_history_service.update_test_variables(
                test_id=self.test_id,
                variables=variables
            )

            logger.info(f"Updated {len(variables)} variables in database for test {self.test_id}")

        except Exception as e:
            logger.error(f"Failed to update variables in database: {str(e)}")
