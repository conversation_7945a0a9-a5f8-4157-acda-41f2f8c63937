"""
Agent loading and management utilities.
"""

import importlib
import importlib.util
import sys
import json
import os
from typing import Dict, List, Optional, Any, Type
from pathlib import Path

from app.core.logging import get_logger
from app.core.timezone_utils import utc_now
from .agent_runtime import ConfigDrivenAgent

logger = get_logger(__name__)


class AgentLoader:
    """Agent loading utilities for both config-driven and code-generated agents."""

    def __init__(self, generated_agents_dir: str = "generated_agents"):
        self.generated_agents_dir = Path(generated_agents_dir)
        self.generated_agents_dir.mkdir(exist_ok=True)

        # 将生成的代理目录添加到Python路径
        if str(self.generated_agents_dir) not in sys.path:
            sys.path.insert(0, str(self.generated_agents_dir))

    def is_config_driven_agent(self, config: Dict) -> bool:
        """检查是否为配置驱动的Agent"""
        # 检查是否有完整的团队配置
        team_plan = config.get("team_plan", {})
        if not team_plan:
            return False

        # 检查是否有团队成员定义
        team_members = team_plan.get("team_members", [])
        if not team_members:
            return False

        # 检查团队成员是否有必要的字段
        for member in team_members:
            if not isinstance(member, dict):
                return False
            if not all(key in member for key in ["name", "role"]):
                return False

        # 检查元数据标记
        metadata = config.get("metadata", {})
        if metadata.get("ready_to_deploy") and not metadata.get("requires_ai_generation", True):
            return True

        # 如果有完整的团队配置，认为可以配置驱动
        return len(team_members) > 0

    def load_agent_config(self, agent_id: str) -> Optional[Dict]:
        """加载Agent配置文件"""
        try:
            # 尝试从生成的代理目录加载配置
            config_path = self.generated_agents_dir / f"{agent_id}_config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"从文件加载Agent配置: {config_path}")
                return config

            # 尝试从其他可能的位置加载
            alternative_paths = [
                Path(f"configs/{agent_id}.json"),
                Path(f"agents/{agent_id}/config.json"),
                Path(f"{agent_id}.json")
            ]

            for path in alternative_paths:
                if path.exists():
                    with open(path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    logger.info(f"从备用路径加载Agent配置: {path}")
                    return config

            logger.warning(f"未找到Agent配置文件: {agent_id}")
            return None

        except Exception as e:
            logger.error(f"加载Agent配置失败 {agent_id}: {str(e)}")
            return None

    async def load_code_generated_agent(self, agent_id: str, config: Dict) -> Optional[Any]:
        """加载代码生成的Agent"""
        try:
            logger.info(f"加载代码生成Agent: {agent_id}")

            # 获取类名
            class_name = config.get("class_name")
            if not class_name:
                logger.error(f"配置中缺少class_name: {agent_id}")
                return None

            # 动态导入Agent类
            agent_class = self._import_agent_class(agent_id, class_name)
            if not agent_class:
                return None

            # 创建实例
            agent_instance = agent_class(config)

            logger.info(f"代码生成Agent {agent_id} 加载成功")
            return agent_instance

        except Exception as e:
            logger.error(f"加载代码生成Agent失败 {agent_id}: {str(e)}")
            return None

    def _import_agent_class(self, agent_id: str, class_name: str) -> Optional[Type]:
        """动态导入Agent类"""
        try:
            # 构建模块名
            module_name = f"{agent_id}_agent"

            # 检查模块文件是否存在
            module_path = self.generated_agents_dir / f"{module_name}.py"
            if not module_path.exists():
                logger.error(f"Agent模块文件不存在: {module_path}")
                return None

            # 动态导入模块
            if module_name in sys.modules:
                # 如果模块已经导入，重新加载
                module = importlib.reload(sys.modules[module_name])
            else:
                # 首次导入
                module = importlib.import_module(module_name)

            # 获取类
            agent_class = getattr(module, class_name, None)
            if not agent_class:
                logger.error(f"模块 {module_name} 中未找到类 {class_name}")
                return None

            return agent_class

        except Exception as e:
            logger.error(f"动态导入失败: {str(e)}")
            return None

    def list_available_agents(self) -> List[str]:
        """列出所有可用的Agent"""
        agents = []

        try:
            # 扫描生成的代理目录
            if self.generated_agents_dir.exists():
                for file_path in self.generated_agents_dir.glob("*_config.json"):
                    agent_id = file_path.stem.replace("_config", "")
                    agents.append(agent_id)

            # 扫描其他可能的配置目录
            config_dirs = [Path("configs"), Path("agents")]
            for config_dir in config_dirs:
                if config_dir.exists():
                    for file_path in config_dir.glob("*.json"):
                        agent_id = file_path.stem
                        if agent_id not in agents:
                            agents.append(agent_id)

            logger.info(f"找到 {len(agents)} 个可用Agent")
            return sorted(agents)

        except Exception as e:
            logger.error(f"列出可用Agent失败: {str(e)}")
            return []

    def get_agent_info(self, agent_id: str, config: Optional[Dict] = None) -> Optional[Dict]:
        """获取Agent信息"""
        try:
            if not config:
                config = self.load_agent_config(agent_id)

            if not config:
                return None

            # 基础信息
            info = {
                "agent_id": agent_id,
                "class_name": config.get("class_name"),
                "team_name": config.get("team_plan", {}).get("team_name"),
                "description": config.get("team_plan", {}).get("description"),
                "generated_at": config.get("generated_at"),
                "version": config.get("version"),
                "metadata": config.get("metadata", {}),
                "is_config_driven": self.is_config_driven_agent(config),
                "status": "available"
            }

            # 团队信息
            team_plan = config.get("team_plan", {})
            if team_plan:
                info["team_info"] = {
                    "team_name": team_plan.get("team_name"),
                    "description": team_plan.get("description"),
                    "team_members": team_plan.get("team_members", []),
                    "workflow_steps": len(team_plan.get("workflow", {}).get("steps", []))
                }

            # 文件信息
            config_path = self.generated_agents_dir / f"{agent_id}_config.json"
            if config_path.exists():
                stat = config_path.stat()
                info["file_info"] = {
                    "config_path": str(config_path),
                    "config_size": stat.st_size,
                    "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }

                # 检查代码文件
                code_path = self.generated_agents_dir / f"{agent_id}_agent.py"
                if code_path.exists():
                    code_stat = code_path.stat()
                    info["file_info"]["code_path"] = str(code_path)
                    info["file_info"]["code_size"] = code_stat.st_size

            return info

        except Exception as e:
            logger.error(f"获取Agent信息失败 {agent_id}: {str(e)}")
            return None

    def validate_agent_config(self, config: Dict) -> Dict[str, Any]:
        """验证Agent配置的完整性"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "score": 0,
            "max_score": 100
        }

        try:
            # 检查基本字段
            required_fields = ["agent_id", "team_name"]
            for field in required_fields:
                if not config.get(field):
                    validation_result["errors"].append(f"Missing required field: {field}")
                    validation_result["is_valid"] = False
                else:
                    validation_result["score"] += 10

            # 检查团队配置
            team_plan = config.get("team_plan", {})
            if not team_plan:
                validation_result["errors"].append("Missing team_plan configuration")
                validation_result["is_valid"] = False
            else:
                validation_result["score"] += 20

                # 检查团队成员
                team_members = team_plan.get("team_members", [])
                if not team_members:
                    validation_result["warnings"].append("No team members defined")
                else:
                    validation_result["score"] += 20

                    # 检查团队成员字段
                    for i, member in enumerate(team_members):
                        if not isinstance(member, dict):
                            validation_result["errors"].append(f"Team member {i} is not a dictionary")
                            validation_result["is_valid"] = False
                            continue

                        member_required = ["name", "role"]
                        for field in member_required:
                            if not member.get(field):
                                validation_result["errors"].append(f"Team member {i} missing {field}")
                                validation_result["is_valid"] = False

                # 检查工作流
                workflow = team_plan.get("workflow", {})
                if not workflow:
                    validation_result["warnings"].append("No workflow defined")
                else:
                    validation_result["score"] += 20

                    steps = workflow.get("steps", [])
                    if not steps:
                        validation_result["warnings"].append("No workflow steps defined")
                    else:
                        validation_result["score"] += 20

                        # 检查工作流步骤
                        for i, step in enumerate(steps):
                            if not isinstance(step, dict):
                                validation_result["errors"].append(f"Workflow step {i} is not a dictionary")
                                validation_result["is_valid"] = False
                                continue

                            step_required = ["name", "assignee"]
                            for field in step_required:
                                if not step.get(field):
                                    validation_result["warnings"].append(f"Workflow step {i} missing {field}")

            # 检查AI配置
            ai_config = config.get("ai_config", {})
            if ai_config:
                validation_result["score"] += 10

            # 最终评分
            validation_result["score"] = min(validation_result["score"], validation_result["max_score"])

            return validation_result

        except Exception as e:
            logger.error(f"配置验证失败: {str(e)}")
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")
            return validation_result

    def cleanup_agent_files(self, agent_id: str) -> Dict[str, Any]:
        """清理Agent相关文件"""
        cleanup_result = {
            "success": True,
            "cleaned_files": [],
            "errors": []
        }

        try:
            # 清理配置文件
            config_path = self.generated_agents_dir / f"{agent_id}_config.json"
            if config_path.exists():
                config_path.unlink()
                cleanup_result["cleaned_files"].append(str(config_path))

            # 清理代码文件
            code_path = self.generated_agents_dir / f"{agent_id}_agent.py"
            if code_path.exists():
                code_path.unlink()
                cleanup_result["cleaned_files"].append(str(code_path))

            # 清理缓存文件
            cache_patterns = [
                f"{agent_id}_agent.pyc",
                f"__pycache__/{agent_id}_agent.*.pyc"
            ]

            for pattern in cache_patterns:
                for cache_file in self.generated_agents_dir.glob(pattern):
                    if cache_file.exists():
                        cache_file.unlink()
                        cleanup_result["cleaned_files"].append(str(cache_file))

            logger.info(f"Agent文件清理完成: {agent_id}, 清理了 {len(cleanup_result['cleaned_files'])} 个文件")

        except Exception as e:
            logger.error(f"清理Agent文件失败 {agent_id}: {str(e)}")
            cleanup_result["success"] = False
            cleanup_result["errors"].append(str(e))

        return cleanup_result
