"""
Dynamic loader - Supports both config-driven and code-generated agent deployment.
Refactored version with improved modularity.
"""

import sys
from typing import Dict, List, Optional, Any
from pathlib import Path

from app.core.logging import get_logger
from app.core.timezone_utils import utc_now
from .agent_runtime import ConfigDrivenAgent
from .agent_loader import AgentLoader

logger = get_logger(__name__)


class DynamicLoader:
    """动态加载器 - 支持配置驱动和代码生成两种方式"""

    def __init__(self):
        self.loaded_agents = {}  # agent_id -> agent_instance
        self.agent_classes = {}  # agent_id -> agent_class
        self.agent_configs = {}  # agent_id -> config
        
        # Initialize agent loader
        self.agent_loader = AgentLoader()

    async def load_agent(self, agent_id: str, agent_config: Optional[Dict] = None, force_reload: bool = False) -> Optional[Any]:
        """加载Agent实例 - 支持配置驱动和代码生成两种方式"""
        logger.info(f"加载Agent: {agent_id}, force_reload={force_reload}")

        try:
            # 如果已经加载且不强制重新加载，直接返回
            if agent_id in self.loaded_agents and not force_reload:
                logger.info(f"Agent {agent_id} 已加载，直接返回")
                return self.loaded_agents[agent_id]

            # 优先使用传入的配置（来自数据库）
            config = agent_config
            if not config:
                # 回退到查找配置文件
                config = self.agent_loader.load_agent_config(agent_id)

            if not config:
                logger.error(f"未找到Agent配置: {agent_id}")
                return None

            # 检查是否为完整的配置驱动Agent
            if self.agent_loader.is_config_driven_agent(config):
                logger.info(f"创建配置驱动Agent: {agent_id}")
                agent_instance = ConfigDrivenAgent(config)

                # 缓存
                self.loaded_agents[agent_id] = agent_instance
                self.agent_configs[agent_id] = config

                logger.info(f"配置驱动Agent {agent_id} 创建成功")
                return agent_instance

            # 回退到传统的代码生成方式
            logger.info(f"回退到代码生成方式加载Agent: {agent_id}")
            return await self._load_code_generated_agent(agent_id, config)

        except Exception as e:
            logger.error(f"加载Agent失败 {agent_id}: {str(e)}")
            return None

    async def _load_code_generated_agent(self, agent_id: str, config: Dict) -> Optional[Any]:
        """Load code-generated agent using the agent loader."""
        try:
            agent_instance = await self.agent_loader.load_code_generated_agent(agent_id, config)
            
            if agent_instance:
                # 缓存
                self.loaded_agents[agent_id] = agent_instance
                self.agent_configs[agent_id] = config
                
                logger.info(f"代码生成Agent {agent_id} 加载成功")
            
            return agent_instance

        except Exception as e:
            logger.error(f"加载代码生成Agent失败 {agent_id}: {str(e)}")
            return None

    async def execute_agent(self, agent_id: str, input_data: Dict, agent_config: Optional[Dict] = None, 
                          progress_callback=None, current_user=None, test_id: Optional[str] = None) -> Dict:
        """执行Agent - 支持配置驱动和代码生成两种方式"""
        logger.info(f"执行Agent: {agent_id}")

        try:
            # 加载Agent
            agent = await self.load_agent(agent_id, agent_config)
            if not agent:
                return {
                    "status": "error",
                    "error": f"Failed to load agent: {agent_id}",
                    "agent_id": agent_id,
                    "executed_at": utc_now().isoformat()
                }

            # Set user context for config-driven agents
            if hasattr(agent, 'set_current_user'):
                agent.set_current_user(current_user)

            # Set test ID for variable tracking
            if test_id and hasattr(agent, 'set_test_id'):
                agent.set_test_id(test_id)

            # 执行Agent
            if hasattr(agent, 'execute') and callable(getattr(agent, 'execute')):
                # For ConfigDrivenAgent, pass progress_callback
                if hasattr(agent, 'team_name'):  # ConfigDrivenAgent has team_name
                    result = await agent.execute(input_data, progress_callback)
                else:
                    result = await agent.execute(input_data)
            else:
                result = await agent.execute(input_data)

            logger.info(f"Agent {agent_id} 执行完成")

            # 确保返回结果包含必要字段
            if isinstance(result, dict):
                result["agent_id"] = agent_id
                if "status" not in result:
                    result["status"] = "completed"
                return result
            else:
                return {
                    "agent_id": agent_id,
                    "status": "completed",
                    "result": result,
                    "executed_at": utc_now().isoformat()
                }

        except Exception as e:
            logger.error(f"执行Agent失败 {agent_id}: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "agent_id": agent_id,
                "executed_at": utc_now().isoformat()
            }

    def unload_agent(self, agent_id: str) -> bool:
        """卸载Agent"""
        logger.info(f"卸载Agent: {agent_id}")

        try:
            # 从缓存中移除
            if agent_id in self.loaded_agents:
                del self.loaded_agents[agent_id]

            if agent_id in self.agent_classes:
                del self.agent_classes[agent_id]

            if agent_id in self.agent_configs:
                del self.agent_configs[agent_id]

            # 从模块缓存中移除
            module_name = f"{agent_id}_agent"
            if module_name in sys.modules:
                del sys.modules[module_name]

            logger.info(f"Agent {agent_id} 卸载成功")
            return True

        except Exception as e:
            logger.error(f"卸载Agent失败 {agent_id}: {str(e)}")
            return False

    def reload_agent(self, agent_id: str) -> bool:
        """重新加载Agent"""
        logger.info(f"重新加载Agent: {agent_id}")

        try:
            # 先卸载
            self.unload_agent(agent_id)

            # 重新加载
            import asyncio
            agent = asyncio.run(self.load_agent(agent_id, force_reload=True))

            return agent is not None

        except Exception as e:
            logger.error(f"重新加载Agent失败 {agent_id}: {str(e)}")
            return False

    def list_available_agents(self) -> List[str]:
        """列出所有可用的Agent"""
        return self.agent_loader.list_available_agents()

    def get_agent_info(self, agent_id: str) -> Optional[Dict]:
        """获取Agent信息"""
        try:
            config = self.agent_configs.get(agent_id)
            if not config:
                config = self.agent_loader.load_agent_config(agent_id)

            if not config:
                return None

            # Get basic info from loader
            info = self.agent_loader.get_agent_info(agent_id, config)
            if not info:
                return None

            # Add runtime information
            info.update({
                "is_loaded": agent_id in self.loaded_agents,
                "status": "active" if agent_id in self.loaded_agents else "inactive"
            })

            # Add execution capabilities
            if agent_id in self.loaded_agents:
                agent = self.loaded_agents[agent_id]
                info["runtime_info"] = {
                    "type": "config_driven" if hasattr(agent, 'team_name') else "code_generated",
                    "has_execute_method": hasattr(agent, 'execute'),
                    "supports_progress_callback": hasattr(agent, 'team_name'),
                    "supports_variable_tracking": hasattr(agent, 'set_test_id')
                }

            return info

        except Exception as e:
            logger.error(f"获取Agent信息失败 {agent_id}: {str(e)}")
            return None

    def get_agent_execution_history(self, agent_id: str, limit: int = 10) -> List[Dict]:
        """获取Agent执行历史"""
        try:
            if agent_id in self.loaded_agents:
                agent = self.loaded_agents[agent_id]
                if hasattr(agent, 'get_execution_history'):
                    return agent.get_execution_history(limit)

            return []

        except Exception as e:
            logger.error(f"获取执行历史失败 {agent_id}: {str(e)}")
            return []

    def validate_agent_config(self, config: Dict) -> Dict[str, Any]:
        """验证Agent配置"""
        return self.agent_loader.validate_agent_config(config)

    def cleanup_agent_files(self, agent_id: str) -> Dict[str, Any]:
        """清理Agent文件"""
        try:
            # First unload the agent
            self.unload_agent(agent_id)
            
            # Then cleanup files
            return self.agent_loader.cleanup_agent_files(agent_id)

        except Exception as e:
            logger.error(f"清理Agent失败 {agent_id}: {str(e)}")
            return {
                "success": False,
                "cleaned_files": [],
                "errors": [str(e)]
            }

    def cleanup_all_agents(self) -> Dict[str, Any]:
        """清理所有Agent"""
        cleanup_result = {
            "success": True,
            "cleaned_agents": [],
            "errors": []
        }

        try:
            # 获取所有已加载的Agent
            loaded_agent_ids = list(self.loaded_agents.keys())

            # 卸载所有Agent
            for agent_id in loaded_agent_ids:
                try:
                    self.unload_agent(agent_id)
                    cleanup_result["cleaned_agents"].append(agent_id)
                except Exception as e:
                    cleanup_result["errors"].append(f"Failed to unload {agent_id}: {str(e)}")

            # 清理模块缓存
            modules_to_remove = []
            for module_name in sys.modules:
                if module_name.endswith("_agent"):
                    modules_to_remove.append(module_name)

            for module_name in modules_to_remove:
                try:
                    del sys.modules[module_name]
                except Exception as e:
                    cleanup_result["errors"].append(f"Failed to remove module {module_name}: {str(e)}")

            logger.info(f"清理完成: 卸载了 {len(cleanup_result['cleaned_agents'])} 个Agent")

        except Exception as e:
            logger.error(f"清理过程出错: {str(e)}")
            cleanup_result["errors"].append(str(e))
            cleanup_result["success"] = False

        return cleanup_result

    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        return {
            "total_agents": len(self.list_available_agents()),
            "loaded_agents": len(self.loaded_agents),
            "memory_usage": {
                "loaded_classes": len(self.agent_classes),
                "cached_configs": len(self.agent_configs)
            },
            "generated_agents_dir": str(self.agent_loader.generated_agents_dir),
            "python_path_added": str(self.agent_loader.generated_agents_dir) in sys.path,
            "last_updated": utc_now().isoformat()
        }


# Global instance
_dynamic_loader = None


def get_dynamic_loader() -> DynamicLoader:
    """Get the global dynamic loader instance."""
    global _dynamic_loader
    if _dynamic_loader is None:
        _dynamic_loader = DynamicLoader()
    return _dynamic_loader
