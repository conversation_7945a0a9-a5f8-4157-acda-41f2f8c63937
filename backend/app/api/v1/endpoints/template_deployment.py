"""
Template deployment endpoints.
"""

import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError, ValidationError
from app.core.logging import api_logger
from app.models.planning import (
    Template, TemplateCreate, TemplateResponse, TemplateFromAgentRequest,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent
from .template_utils import parse_template_json_fields, build_template_response_dict

router = APIRouter()


@router.get("/{template_id}/agent-config", response_model=Dict[str, Any])
async def get_template_agent_config(
    template_id: str,
    customizations: Optional[str] = Query(None, description="JSON string of customizations"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get complete agent configuration from template for direct deployment."""
    try:
        from app.services.template_management_service import TemplateManagementService

        # Parse customizations if provided
        custom_dict = {}
        if customizations:
            try:
                import json
                custom_dict = json.loads(customizations)
            except (json.JSONDecodeError, TypeError):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid customizations JSON format"
                )

        # Get complete agent configuration from template
        template_service = TemplateManagementService(db)
        agent_config = await template_service.transform_template_for_agent_creation(
            template_id, current_user, custom_dict if custom_dict else None
        )

        # Add deployment readiness information
        response = {
            "template_id": template_id,
            "agent_config": agent_config,
            "ready_to_deploy": agent_config["metadata"]["ready_to_deploy"],
            "requires_ai_generation": agent_config["metadata"]["requires_ai_generation"],
            "team_members_count": len(agent_config.get("team_members", [])),
            "has_workflow": bool(agent_config.get("team_plan", {}).get("workflow", {}).get("steps")),
            "customizations_applied": bool(custom_dict),
            "deployment_method": "config_driven" if agent_config["metadata"]["ready_to_deploy"] else "ai_generation",
        }

        api_logger.info(
            "Template agent config retrieved",
            template_id=template_id,
            user_id=current_user.id,
            ready_to_deploy=response["ready_to_deploy"],
            team_members_count=response["team_members_count"],
        )

        return response

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template '{template_id}' not found"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to get template agent config {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template agent config: {str(e)}",
        )


@router.get("/complete-library", response_model=List[Dict[str, Any]])
async def get_complete_template_library(
    include_user_templates: bool = Query(True, description="Include user's private templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[Dict[str, Any]]:
    """Get complete template library including system templates and user templates."""
    try:
        templates = []

        # Get system/deployable templates
        from app.services.complete_templates import get_complete_templates_service
        complete_templates_service = get_complete_templates_service()
        system_templates = complete_templates_service.get_all_templates()

        # Add system template marker and enhance with deployment info
        for template in system_templates:
            enhanced_template = template.copy()
            enhanced_template.update({
                "source": "system",
                "is_deployable": True,
                "ready_to_deploy": True,
                "template_type": "complete_deployable",
                "is_owner": False,
                "can_edit": False,
            })
            templates.append(enhanced_template)

        # Get user templates from database if requested
        if include_user_templates:
            query = """
                SELECT template_id, name, description, category, difficulty,
                       visibility, status, tags, usage_count, rating, rating_count,
                       version, author_name, use_case, created_at, updated_at, user_id,
                       team_structure_template, template_metadata
                FROM templates
                WHERE (visibility IN ('public', 'featured') OR user_id = :user_id)
                AND status = 'active'
                ORDER BY created_at DESC
            """
            result = await db.execute(text(query), {"user_id": current_user.id})
            db_templates = result.fetchall()

            for template in db_templates:
                template_dict = {column: getattr(template, column) for column in template._fields}

                # Parse JSON fields
                if template_dict.get("tags") and isinstance(template_dict["tags"], str):
                    try:
                        import json
                        template_dict["tags"] = json.loads(template_dict["tags"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["tags"] = []

                if template_dict.get("team_structure_template") and isinstance(template_dict["team_structure_template"], str):
                    try:
                        import json
                        template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["team_structure_template"] = {}

                if template_dict.get("template_metadata") and isinstance(template_dict["template_metadata"], str):
                    try:
                        import json
                        template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
                    except (json.JSONDecodeError, TypeError):
                        template_dict["template_metadata"] = {}

                # Check if template is ready to deploy
                team_structure = template_dict.get("team_structure_template", {})
                metadata = template_dict.get("template_metadata", {})
                is_ready = (
                    metadata.get("ready_to_deploy", False) and
                    bool(team_structure.get("team_members")) and
                    not metadata.get("requires_ai_generation", True)
                )

                enhanced_template = {
                    "id": template_dict["template_id"],
                    "name": template_dict["name"],
                    "description": template_dict["description"],
                    "category": template_dict["category"],
                    "difficulty": template_dict["difficulty"],
                    "use_case": template_dict["use_case"],
                    "tags": template_dict["tags"],
                    "usage_count": template_dict["usage_count"],
                    "rating": template_dict["rating"],
                    "author_name": template_dict["author_name"],
                    "created_at": template_dict["created_at"].isoformat() if template_dict["created_at"] else None,
                    "source": "user",
                    "is_deployable": is_ready,
                    "ready_to_deploy": is_ready,
                    "template_type": metadata.get("template_type", "user_created"),
                    "is_owner": template_dict["user_id"] == current_user.id,
                    "can_edit": template_dict["user_id"] == current_user.id,
                    "team_members_count": len(team_structure.get("team_members", [])),
                    "has_workflow": bool(team_structure.get("workflow", {}).get("steps")),
                }
                templates.append(enhanced_template)

        api_logger.info(
            "Complete template library retrieved",
            user_id=current_user.id,
            total_templates=len(templates),
            system_templates=len(system_templates),
            user_templates=len(templates) - len(system_templates),
        )

        return templates

    except Exception as e:
        api_logger.error(f"Failed to get complete template library: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get complete template library: {str(e)}",
        )


@router.post("/from-agent", response_model=TemplateResponse)
async def create_template_from_agent(
    request: TemplateFromAgentRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a template from an existing agent."""
    try:
        # Check if agent exists and belongs to user
        agent_query = """
            SELECT * FROM agents 
            WHERE agent_id = :agent_id AND user_id = :user_id
        """
        result = await db.execute(
            text(agent_query), 
            {"agent_id": request.agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", request.agent_id)
        
        # Extract agent data for template
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Parse team_plan if it's a JSON string (to avoid double serialization)
        team_plan = agent_dict.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                import json
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}

        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Create template from agent data
        template = Template(
            template_id=template_id,
            name=request.name,
            description=request.description,
            category=request.category,
            difficulty=request.difficulty,
            visibility=request.visibility,
            status=TemplateStatus.ACTIVE,
            prompt_template=agent_dict.get("prompt_template", ""),
            team_structure_template=team_plan,  # Use parsed team_plan
            default_config={},
            tags=request.tags or [],
            keywords=request.keywords or [],
            use_case=request.use_case,
            source_agent_id=request.agent_id,
            template_metadata={
                "created_from_agent": True,
                "source_agent_name": agent_dict.get("team_name", ""),
            },
            user_id=current_user.id,
            author_name=current_user.name,
            usage_count=0,
            rating_count=0,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)
        
        api_logger.info(
            "Template created from agent",
            template_id=template_id,
            source_agent_id=request.agent_id,
            user_id=current_user.id,
        )
        
        # Build response dictionary
        template_dict = build_template_response_dict(template, current_user.id)

        return TemplateResponse(**template_dict)
        
    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to create template from agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template from agent: {str(e)}",
        )
