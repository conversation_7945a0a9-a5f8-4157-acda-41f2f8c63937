"""
Agent CRUD operations endpoints.
"""

from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from pydantic import BaseModel

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError
from app.core.logging import api_logger
from app.core.timezone_utils import utc_now
from app.services.logging_service import log_agent_operation
from app.models.agent import (
    Agent, AgentStatus, AgentResponse, AgentWithTeamResponse, AgentUpdate
)
from app.models.user import User
from app.models.application_log import EventType


# Pagination response models
class PaginatedAgentResponse(BaseModel):
    """Paginated agent response model."""
    data: List[AgentWithTeamResponse]
    total: int
    page: int
    size: int
    total_pages: int

router = APIRouter()


async def get_agent_by_id(agent_id: str, current_user: User, db: AsyncSession):
    """Helper function to get agent by ID with user ownership check."""
    query = """
        SELECT * FROM agents 
        WHERE agent_id = :agent_id AND user_id = :user_id
    """
    params = {"agent_id": agent_id, "user_id": current_user.id}

    result = await db.execute(text(query), params)
    agent = result.fetchone()

    if not agent:
        raise NotFoundError("Agent", agent_id)

    return agent


@router.get("/", response_model=PaginatedAgentResponse)
async def list_agents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status_filter: Optional[AgentStatus] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> PaginatedAgentResponse:
    """List agents with filtering and pagination."""
    try:
        # Build query - exclude deleted agents by default and filter by user
        query = "SELECT * FROM agents WHERE status != :deleted_status AND user_id = :user_id"
        params = {"deleted_status": AgentStatus.DELETED, "user_id": current_user.id}

        if status_filter:
            query += " AND status = :status_filter"
            params["status_filter"] = status_filter

        if search:
            query += " AND (team_name ILIKE :search OR description ILIKE :search)"
            params["search"] = f"%{search}%"

        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        params.update({"limit": limit, "skip": skip})

        result = await db.execute(text(query), params)
        agents = result.fetchall()

        # Convert to response models
        agent_responses = []
        for agent in agents:
            # Convert Row to dict
            agent_dict = {column: getattr(agent, column) for column in agent._fields}
            
            # Parse team_plan if it's a JSON string
            team_plan = agent_dict.get("team_plan", {})
            if isinstance(team_plan, str):
                try:
                    import json
                    team_plan = json.loads(team_plan)
                except (json.JSONDecodeError, TypeError):
                    team_plan = {}
            elif team_plan is None:
                team_plan = {}

            # Parse team_members if it's a JSON string
            team_members = agent_dict.get("team_members", [])
            if isinstance(team_members, str):
                try:
                    import json
                    team_members = json.loads(team_members)
                except (json.JSONDecodeError, TypeError):
                    team_members = []
            elif team_members is None:
                team_members = []

            # Update agent_dict with parsed data
            agent_dict["team_plan"] = team_plan
            agent_dict["team_members"] = team_members

            # Convert enum values from database format to expected format
            if "agent_type" in agent_dict and isinstance(agent_dict["agent_type"], str):
                # Convert from uppercase to lowercase if needed
                agent_dict["agent_type"] = agent_dict["agent_type"].lower()

            if "status" in agent_dict and isinstance(agent_dict["status"], str):
                # Convert from uppercase to lowercase if needed
                agent_dict["status"] = agent_dict["status"].lower()

            # Create response with parsed data
            agent_response = AgentWithTeamResponse(**agent_dict)
            agent_responses.append(agent_response)

        # Get total count for pagination
        count_query = "SELECT COUNT(*) FROM agents WHERE status != :deleted_status AND user_id = :user_id"
        count_params = {"deleted_status": AgentStatus.DELETED, "user_id": current_user.id}

        if status_filter:
            count_query += " AND status = :status_filter"
            count_params["status_filter"] = status_filter

        if search:
            count_query += " AND (team_name ILIKE :search OR description ILIKE :search)"
            count_params["search"] = f"%{search}%"

        count_result = await db.execute(text(count_query), count_params)
        total = count_result.scalar()

        # Calculate pagination info
        page = (skip // limit) + 1
        total_pages = (total + limit - 1) // limit

        api_logger.info(
            "Agents listed",
            user_id=current_user.id,
            count=len(agent_responses),
            total=total,
            page=page,
            status_filter=status_filter.value if status_filter else None,
            search=search,
        )

        return PaginatedAgentResponse(
            data=agent_responses,
            total=total,
            page=page,
            size=limit,
            total_pages=total_pages
        )

    except Exception as e:
        api_logger.error(f"Failed to list agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list agents: {str(e)}"
        )


@router.get("/{agent_id}", response_model=AgentWithTeamResponse)
async def get_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> AgentWithTeamResponse:
    """Get agent by ID."""
    try:
        agent = await get_agent_by_id(agent_id, current_user, db)

        # Convert Row to dict
        agent_dict = {column: getattr(agent, column) for column in agent._fields}
        
        # Parse team_plan if it's a JSON string
        team_plan = agent_dict.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                import json
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}

        # Parse team_members if it's a JSON string
        team_members = agent_dict.get("team_members", [])
        if isinstance(team_members, str):
            try:
                import json
                team_members = json.loads(team_members)
            except (json.JSONDecodeError, TypeError):
                team_members = []
        elif team_members is None:
            team_members = []

        # Update agent_dict with parsed data
        agent_dict["team_plan"] = team_plan
        agent_dict["team_members"] = team_members

        # Convert enum values from database format to expected format
        if "agent_type" in agent_dict and isinstance(agent_dict["agent_type"], str):
            # Convert from uppercase to lowercase if needed
            agent_dict["agent_type"] = agent_dict["agent_type"].lower()

        if "status" in agent_dict and isinstance(agent_dict["status"], str):
            # Convert from uppercase to lowercase if needed
            agent_dict["status"] = agent_dict["status"].lower()

        # Create response with parsed data
        agent_response = AgentWithTeamResponse(**agent_dict)

        api_logger.info(
            "Agent retrieved",
            agent_id=agent_id,
            user_id=current_user.id,
        )

        return agent_response

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found"
        )
    except Exception as e:
        api_logger.error(f"Failed to get agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent: {str(e)}"
        )


@router.put("/{agent_id}", response_model=AgentResponse)
@router.patch("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: str,
    agent_update: AgentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> AgentResponse:
    """Update agent."""
    try:
        # Check if agent exists and belongs to user
        agent = await get_agent_by_id(agent_id, current_user, db)

        # Build update query dynamically
        update_fields = []
        params = {"agent_id": agent_id, "user_id": current_user.id}

        # Handle each field that can be updated
        update_data = agent_update.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if field in ["team_plan", "team_members"]:
                # JSON fields need special handling
                import json
                update_fields.append(f"{field} = :{field}")
                params[field] = json.dumps(value) if value is not None else None
            else:
                update_fields.append(f"{field} = :{field}")
                params[field] = value

        if not update_fields:
            # No fields to update, return current agent
            agent_dict = {column: getattr(agent, column) for column in agent._fields}
            return AgentResponse(**agent_dict)

        # Add updated_at
        update_fields.append("updated_at = :updated_at")
        params["updated_at"] = utc_now().replace(tzinfo=None)

        # Execute update
        update_query = f"""
            UPDATE agents
            SET {', '.join(update_fields)}
            WHERE agent_id = :agent_id AND user_id = :user_id
        """
        
        await db.execute(text(update_query), params)
        await db.commit()

        # Fetch updated agent
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        updated_agent = result.fetchone()

        api_logger.info(
            "Agent updated",
            agent_id=agent_id,
            user_id=current_user.id,
            updated_fields=list(update_data.keys()),
        )

        # Log agent update event
        await log_agent_operation(
            event_type=EventType.AGENT_UPDATE,
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent updated: {updated_agent.team_name}",
            metadata={
                "updated_fields": list(update_data.keys()),
                "team_name": updated_agent.team_name,
            }
        )

        # Convert to response model with proper data parsing
        agent_dict = {column: getattr(updated_agent, column) for column in updated_agent._fields}

        # Parse team_plan if it's a JSON string
        team_plan = agent_dict.get("team_plan", {})
        if isinstance(team_plan, str):
            try:
                import json
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}
        elif team_plan is None:
            team_plan = {}

        # Parse team_members if it's a JSON string
        team_members = agent_dict.get("team_members", [])
        if isinstance(team_members, str):
            try:
                import json
                team_members = json.loads(team_members)
            except (json.JSONDecodeError, TypeError):
                team_members = []
        elif team_members is None:
            team_members = []

        # Update agent_dict with parsed data
        agent_dict["team_plan"] = team_plan
        agent_dict["team_members"] = team_members

        # Convert enum values from database format to expected format
        if "agent_type" in agent_dict and isinstance(agent_dict["agent_type"], str):
            # Convert from uppercase to lowercase if needed
            agent_dict["agent_type"] = agent_dict["agent_type"].lower()

        if "status" in agent_dict and isinstance(agent_dict["status"], str):
            # Convert from uppercase to lowercase if needed
            agent_dict["status"] = agent_dict["status"].lower()

        return AgentResponse(**agent_dict)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found"
        )
    except Exception as e:
        api_logger.error(f"Failed to update agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update agent: {str(e)}"
        )


@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> dict:
    """Delete agent (soft delete by setting status to DELETED)."""
    try:
        # Check if agent exists and belongs to user
        agent = await get_agent_by_id(agent_id, current_user, db)

        # Soft delete by updating status
        await db.execute(
            text("""
                UPDATE agents 
                SET status = :status, updated_at = :updated_at 
                WHERE agent_id = :agent_id AND user_id = :user_id
            """),
            {
                "status": AgentStatus.DELETED,
                "updated_at": utc_now().replace(tzinfo=None),
                "agent_id": agent_id,
                "user_id": current_user.id
            }
        )
        await db.commit()

        api_logger.info(
            "Agent deleted",
            agent_id=agent_id,
            user_id=current_user.id,
        )

        # Log agent deletion event
        await log_agent_operation(
            event_type=EventType.AGENT_DELETE,
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent deleted: {agent.team_name}",
            metadata={
                "team_name": agent.team_name,
                "deletion_type": "soft_delete",
            }
        )

        return {"message": "Agent deleted successfully"}

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found"
        )
    except Exception as e:
        api_logger.error(f"Failed to delete agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete agent: {str(e)}"
        )
