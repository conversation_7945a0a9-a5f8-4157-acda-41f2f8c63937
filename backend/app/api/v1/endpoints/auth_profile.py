"""
User profile management endpoints.
"""

import uuid
from pathlib import Path
from typing import Dict
from fastapi import APIRouter, Depends, HTTPException, Request, UploadFile, File, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.models.user import User, UserUpdate, UserProfile
from app.services.user_service import UserService
from app.core.logging import api_logger

router = APIRouter()

# Dependency aliases for cleaner code
CurrentUserRequired = Depends(get_current_active_user)
CurrentActiveUser = Depends(get_current_active_user)


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: User = CurrentUserRequired,
) -> UserProfile:
    """Get current user profile."""
    return UserProfile(
        id=current_user.id,
        uuid=current_user.uuid,
        name=current_user.name,
        email=current_user.email,
        role=current_user.role,
        status=current_user.status,
        avatar=current_user.avatar,
        bio=current_user.bio,
        timezone=current_user.timezone,
        language=current_user.language,
        last_login_at=current_user.last_login_at,
        is_email_verified=current_user.is_email_verified,
        login_count=current_user.login_count,
        is_2fa_enabled=current_user.is_2fa_enabled,
        two_fa_enabled_at=current_user.two_fa_enabled_at,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        # Extended profile fields
        preferences=current_user.preferences,
        last_activity_at=current_user.last_activity_at,
        email_verified_at=current_user.email_verified_at,
        password_changed_at=current_user.password_changed_at,
    )


@router.put("/me", response_model=UserProfile)
async def update_user_profile(
    update_data: UserUpdate,
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> UserProfile:
    """Update current user profile."""
    try:
        user_service = UserService(db)

        # Convert to dict and filter None values
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

        updated_user = await user_service.update_user_profile(current_user, update_dict)

        api_logger.info(
            "User profile updated",
            user_id=updated_user.id,
            updated_fields=list(update_dict.keys())
        )

        return UserProfile(
            id=updated_user.id,
            uuid=updated_user.uuid,
            name=updated_user.name,
            email=updated_user.email,
            role=updated_user.role,
            status=updated_user.status,
            avatar=updated_user.avatar,
            bio=updated_user.bio,
            timezone=updated_user.timezone,
            language=updated_user.language,
            last_login_at=updated_user.last_login_at,
            is_email_verified=updated_user.is_email_verified,
            login_count=updated_user.login_count,
            is_2fa_enabled=updated_user.is_2fa_enabled,
            two_fa_enabled_at=updated_user.two_fa_enabled_at,
            created_at=updated_user.created_at,
            updated_at=updated_user.updated_at,
            # Extended profile fields
            preferences=updated_user.preferences,
            last_activity_at=updated_user.last_activity_at,
            email_verified_at=updated_user.email_verified_at,
            password_changed_at=updated_user.password_changed_at,
        )

    except ValueError as e:
        api_logger.warning(
            "Profile update failed - validation error",
            user_id=current_user.id,
            error=str(e),
            update_fields=list(update_data.model_dump(exclude_unset=True).keys())
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(
            "Profile update failed - server error",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed. Please try again later."
        )


@router.post("/upload-avatar")
async def upload_user_avatar(
    file: UploadFile = File(...),
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Upload user avatar."""
    try:
        # Validate file type
        allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."
            )

        # Validate file size (5MB max)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File too large. Maximum size is 5MB."
            )

        # Create upload directory if it doesn't exist
        upload_dir = Path("data/uploads/avatars")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename
        file_extension = Path(file.filename).suffix if file.filename else ".jpg"
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # Save file
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Update user avatar URL
        avatar_url = f"/uploads/avatars/{unique_filename}"
        user_service = UserService(db)
        await user_service.update_user_profile(current_user, {"avatar": avatar_url})

        api_logger.info(
            "Avatar uploaded successfully",
            user_id=current_user.id,
            filename=unique_filename,
            file_size=len(file_content)
        )

        return {"avatar_url": avatar_url}

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Avatar upload failed",
            user_id=current_user.id,
            error=str(e),
            filename=file.filename if file else "unknown"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Avatar upload failed. Please try again later."
        )
