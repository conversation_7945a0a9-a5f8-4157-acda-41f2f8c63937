"""
Template versioning endpoints.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError
from app.core.logging import api_logger
from app.models.planning import (
    Template, TemplateUpdate, TemplateResponse, TemplateListResponse
)
from app.models.user import User
from .template_utils import parse_template_json_fields, build_template_response_dict, build_template_list_response_dict

router = APIRouter()


@router.get("/{template_id}/versions", response_model=List[TemplateListResponse])
async def get_template_versions(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get all versions of a template."""
    try:
        # First check if user has access to the base template
        base_query = """
            SELECT * FROM templates
            WHERE template_id = :template_id
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(base_query),
            {"template_id": template_id, "user_id": current_user.id}
        )
        base_template = result.fetchone()

        if not base_template:
            raise NotFoundError("Template", template_id)

        # Get all versions (templates with this as parent or sharing the same parent)
        versions_query = """
            WITH template_family AS (
                -- Get the root template
                SELECT template_id as root_id FROM templates
                WHERE template_id = :template_id

                UNION

                -- Get the parent if this is a version
                SELECT parent_template_id as root_id FROM templates
                WHERE template_id = :template_id AND parent_template_id IS NOT NULL
            )
            SELECT DISTINCT t.id, t.template_id, t.name, t.description, t.category,
                   t.difficulty, t.visibility, t.status, t.tags, t.usage_count,
                   t.rating, t.rating_count, t.version, t.author_name, t.use_case,
                   t.created_at, t.updated_at, t.user_id
            FROM templates t
            CROSS JOIN template_family tf
            WHERE (t.template_id = tf.root_id OR t.parent_template_id = tf.root_id)
            AND (t.visibility IN ('public', 'featured') OR t.user_id = :user_id)
            ORDER BY t.created_at DESC
        """

        result = await db.execute(text(versions_query), {"template_id": template_id, "user_id": current_user.id})
        versions = result.fetchall()

        # Convert to response models
        version_responses = []
        for version in versions:
            version_dict = build_template_list_response_dict(version, current_user.id)
            version_responses.append(TemplateListResponse(**version_dict))

        return version_responses

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get template versions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template versions: {str(e)}",
        )


@router.post("/{template_id}/create-version", response_model=TemplateResponse)
async def create_template_version(
    template_id: str,
    template_update: TemplateUpdate,
    version: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a new version of an existing template."""
    try:
        # Check if template exists and user owns it
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        original_template = result.fetchone()

        if not original_template:
            raise NotFoundError("Template", template_id)

        # Generate new template ID for the version
        new_template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Get original template data
        original_dict = {column: getattr(original_template, column) for column in original_template._fields}

        # Auto-increment version if not provided
        if not version:
            # Parse current version and increment
            current_version = original_dict.get("version", "1.0.0")
            try:
                parts = current_version.split(".")
                if len(parts) >= 2:
                    major, minor = int(parts[0]), int(parts[1])
                    patch = int(parts[2]) if len(parts) > 2 else 0
                    version = f"{major}.{minor}.{patch + 1}"
                else:
                    version = "1.0.1"
            except (ValueError, IndexError):
                version = "1.0.1"

        # Create new version with updates
        new_template_data = {
            "template_id": new_template_id,
            "name": getattr(template_update, "name", None) or original_dict["name"],
            "description": getattr(template_update, "description", None) or original_dict["description"],
            "category": getattr(template_update, "category", None) or original_dict["category"],
            "difficulty": getattr(template_update, "difficulty", None) or original_dict["difficulty"],
            "visibility": getattr(template_update, "visibility", None) or original_dict["visibility"],
            "status": getattr(template_update, "status", None) or original_dict["status"],
            "prompt_template": getattr(template_update, "prompt_template", None) or original_dict["prompt_template"],
            "team_structure_template": getattr(template_update, "team_structure_template", None) or original_dict["team_structure_template"],
            "default_config": getattr(template_update, "default_config", None) or original_dict["default_config"],
            "tags": getattr(template_update, "tags", None) or original_dict["tags"],
            "keywords": getattr(template_update, "keywords", None) or original_dict["keywords"],
            "use_case": getattr(template_update, "use_case", None) or original_dict["use_case"],
            "example_input": getattr(template_update, "example_input", None) or original_dict["example_input"],
            "expected_output": getattr(template_update, "expected_output", None) or original_dict["expected_output"],
            "version": version,
            "parent_template_id": template_id,  # Link to original
            "source_agent_id": original_dict["source_agent_id"],
            "template_metadata": {
                **original_dict.get("template_metadata", {}),
                "version_of": template_id,
                "version_created_at": datetime.now(timezone.utc).isoformat(),
                "changes": getattr(template_update, "template_metadata", {}).get("changes", "Version update"),
            },
            "user_id": current_user.id,
            "author_name": current_user.username,
            "usage_count": 0,
            "rating_count": 0,
        }

        template = Template(**new_template_data)
        db.add(template)
        await db.commit()
        await db.refresh(template)

        api_logger.info(
            "Template version created",
            original_template_id=template_id,
            new_template_id=new_template_id,
            version=version,
            user_id=current_user.id,
        )

        # Build response dictionary
        template_dict = build_template_response_dict(template, current_user.id)

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to create template version: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template version: {str(e)}",
        )


@router.post("/{template_id}/rollback/{version_template_id}", response_model=TemplateResponse)
async def rollback_template(
    template_id: str,
    version_template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Rollback template to a previous version."""
    try:
        # Check if user owns the main template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        main_template = result.fetchone()

        if not main_template:
            raise NotFoundError("Template", template_id)

        # Check if version template exists and is related
        result = await db.execute(
            text("""
                SELECT * FROM templates
                WHERE template_id = :version_template_id
                AND (parent_template_id = :template_id OR template_id = :template_id)
                AND user_id = :user_id
            """),
            {
                "version_template_id": version_template_id,
                "template_id": template_id,
                "user_id": current_user.id
            }
        )
        version_template = result.fetchone()

        if not version_template:
            raise NotFoundError("Template version", version_template_id)

        # Copy version data to main template
        version_dict = {column: getattr(version_template, column) for column in version_template._fields}

        # Update main template with version data
        update_fields = [
            "name = :name",
            "description = :description",
            "category = :category",
            "difficulty = :difficulty",
            "visibility = :visibility",
            "status = :status",
            "prompt_template = :prompt_template",
            "team_structure_template = :team_structure_template",
            "default_config = :default_config",
            "tags = :tags",
            "keywords = :keywords",
            "use_case = :use_case",
            "example_input = :example_input",
            "expected_output = :expected_output",
            "version = :version",
            "template_metadata = :template_metadata",
            "updated_at = :updated_at"
        ]

        params = {
            "template_id": template_id,
            "user_id": current_user.id,
            "name": version_dict["name"],
            "description": version_dict["description"],
            "category": version_dict["category"],
            "difficulty": version_dict["difficulty"],
            "visibility": version_dict["visibility"],
            "status": version_dict["status"],
            "prompt_template": version_dict["prompt_template"],
            "team_structure_template": version_dict["team_structure_template"],
            "default_config": version_dict["default_config"],
            "tags": version_dict["tags"],
            "keywords": version_dict["keywords"],
            "use_case": version_dict["use_case"],
            "example_input": version_dict["example_input"],
            "expected_output": version_dict["expected_output"],
            "version": version_dict["version"],
            "template_metadata": version_dict["template_metadata"],
            "updated_at": datetime.now(timezone.utc).replace(tzinfo=None)
        }

        update_query = f"""
            UPDATE templates
            SET {', '.join(update_fields)}
            WHERE template_id = :template_id AND user_id = :user_id
        """

        await db.execute(text(update_query), params)
        await db.commit()

        # Fetch updated template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id"),
            {"template_id": template_id}
        )
        updated_template = result.fetchone()

        api_logger.info(
            "Template rolled back",
            template_id=template_id,
            version_template_id=version_template_id,
            user_id=current_user.id,
        )

        # Build response dictionary
        template_dict = build_template_response_dict(updated_template, current_user.id)

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to rollback template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to rollback template: {str(e)}",
        )
