"""
Agent creation endpoints.
"""

import uuid
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import AgentCreationError, ValidationError
from app.core.logging import api_logger
from app.core.timezone_utils import utc_now
from app.services.logging_service import log_agent_operation
from app.models.agent import Agent, AgentStatus, AgentType
from app.models.application_log import EventType
from app.models.user import User

router = APIRouter()


@router.post("/from-template", response_model=Dict[str, Any])
async def create_agent_from_template(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Create agent directly from a complete template (no AI generation needed)."""
    try:
        template_id = request.get("template_id")
        customizations = request.get("customizations", {})

        if not template_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="template_id is required"
            )

        # Get template configuration
        from app.services.template_management_service import TemplateManagementService
        template_service = TemplateManagementService(db)
        agent_config = await template_service.transform_template_for_agent_creation(
            template_id, current_user, customizations
        )

        # Generate agent ID
        agent_id = f"agent_{uuid.uuid4().hex[:12]}"

        # Extract team members for database storage
        team_members = agent_config.get("team_members", [])

        # Determine initial status based on template completeness
        initial_status = AgentStatus.ACTIVE if agent_config["metadata"]["ready_to_deploy"] else AgentStatus.CREATING

        # Create agent record
        agent = Agent(
            agent_id=agent_id,
            team_name=agent_config.get("team_name", "Unnamed Team"),
            description=agent_config.get("description", ""),
            team_plan=agent_config["team_plan"],
            team_members=team_members,
            prompt_template=agent_config.get("prompt_template", ""),
            system_prompt=agent_config.get("system_prompt", ""),
            user_id=current_user.id,
            status=initial_status,
            agent_type=AgentType.TEAM,
            usage_count=0,
        )

        db.add(agent)
        await db.commit()
        await db.refresh(agent)

        # Generate API endpoint URL
        api_endpoint = f"http://localhost:8000/api/v1/agents/{agent_id}/execute"

        response = {
            "agent_id": agent_id,
            "status": initial_status.value,
            "api_endpoint": api_endpoint,
            "ready_to_deploy": agent_config["metadata"]["ready_to_deploy"],
            "created_from_template": True,
            "template_id": template_id,
        }

        if agent_config["metadata"]["ready_to_deploy"]:
            # Agent is ready to use immediately
            response.update({
                "message": "Agent created successfully from template and is ready to use",
                "estimated_time": "0 seconds",
            })

            api_logger.info(
                "Agent created directly from complete template",
                agent_id=agent_id,
                template_id=template_id,
                team_name=agent.team_name,
                team_members_count=len(team_members),
            )
        else:
            # Agent needs AI generation (fallback for incomplete templates)
            background_tasks.add_task(
                process_agent_creation,
                agent.id,
                agent_config["team_plan"],
            )

            response.update({
                "message": "Agent creation started (template requires AI enhancement)",
                "estimated_time": "60-120 seconds",
            })

            api_logger.info(
                "Agent creation started from template (requires AI enhancement)",
                agent_id=agent_id,
                template_id=template_id,
                team_name=agent.team_name,
            )

        # Log agent creation event
        await log_agent_operation(
            event_type=EventType.AGENT_CREATE,
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent created from template: {agent.team_name}",
            metadata={
                "template_id": template_id,
                "team_name": agent.team_name,
                "ready_to_deploy": agent_config["metadata"]["ready_to_deploy"],
                "team_members_count": len(team_members),
                "source": "template"
            }
        )

        return response

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to create agent from template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create agent from template: {str(e)}"
        )


@router.post("/", response_model=Dict[str, Any])
async def create_agent(
    team_plan: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Create a new agent from team plan."""
    try:
        # Validate team plan
        if not team_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Team plan is required"
            )

        # Generate unique agent ID
        agent_id = f"agent_{uuid.uuid4().hex[:12]}"

        # Extract team members for database storage
        team_members = team_plan.get("team_members", [])
        if not team_members:
            # Try to extract from specialists (legacy support)
            specialists = team_plan.get("specialists", [])
            team_members = [
                {
                    "name": spec.get("name", ""),
                    "role": spec.get("role", ""),
                    "description": spec.get("description", ""),
                    "system_prompt": spec.get("system_prompt", "")
                }
                for spec in specialists
            ]

        # Create agent record with complete team information
        agent = Agent(
            agent_id=agent_id,
            team_name=team_plan.get("team_name", "Unnamed Team"),
            description=team_plan.get("description", ""),
            team_plan=team_plan,  # Store complete team plan
            team_members=team_members,  # Store extracted team members
            prompt_template=team_plan.get("prompt_template", ""),
            system_prompt=team_plan.get("system_prompt", ""),
            user_id=current_user.id,  # Associate with current user
            status=AgentStatus.CREATING,
            agent_type=AgentType.TEAM,
            usage_count=0,
        )

        db.add(agent)
        await db.commit()
        await db.refresh(agent)
        
        # Start agent creation process in background
        background_tasks.add_task(
            process_agent_creation,
            agent.id,
            team_plan,
        )
        
        api_logger.info(
            "Agent creation started",
            agent_id=agent_id,
            team_name=agent.team_name,
        )

        # Log agent creation event
        await log_agent_operation(
            event_type="agent_create",
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent creation started: {agent.team_name}",
            metadata={
                "team_name": agent.team_name,
                "description": agent.description,
                "team_members_count": len(team_members),
                "agent_type": agent.agent_type.value
            }
        )
        
        # Generate API endpoint URL (full URL for frontend display)
        api_endpoint = f"http://localhost:8000/api/v1/agents/{agent_id}/execute"

        return {
            "agent_id": agent_id,
            "status": "creating",
            "message": "Agent creation started",
            "api_endpoint": api_endpoint,
            "estimated_time": "60-120 seconds",
        }

    except Exception as e:
        api_logger.error(f"Failed to create agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create agent: {str(e)}"
        )


async def process_agent_creation(agent_db_id: int, team_plan: Dict[str, Any]) -> None:
    """Process agent creation in background."""
    from app.core.database import get_session

    async for db in get_session():
        try:
            # Get agent record
            result = await db.execute(
                text("SELECT * FROM agents WHERE id = :id"),
                {"id": agent_db_id}
            )
            agent = result.fetchone()

            if not agent:
                api_logger.error(f"Agent not found for background processing: {agent_db_id}")
                return

            agent_id = agent.agent_id

            # Process agent creation (simplified for now)
            # In a full implementation, this would generate actual agent code
            # For now, we just mark the agent as active

            # Update agent status to active
            await db.execute(
                text("UPDATE agents SET status = :status WHERE id = :id"),
                {"status": AgentStatus.ACTIVE, "id": agent_db_id}
            )
            await db.commit()

            api_logger.info(
                "Agent creation completed successfully",
                agent_id=agent_id,
                team_name=agent.team_name if agent else "Unknown",
            )

            # Log completion
            await log_agent_operation(
                event_type=EventType.AGENT_CREATE,
                agent_id=agent_id,
                user_id=agent.user_id if agent else None,
                message=f"Agent creation completed: {agent.team_name if agent else 'Unknown'}",
                metadata={
                    "team_name": agent.team_name if agent else "Unknown",
                    "team_plan": team_plan,
                    "status": "completed"
                }
            )

        except Exception as e:
            api_logger.error(f"Background agent creation failed: {str(e)}")

            # Update agent status to error
            try:
                await db.execute(
                    text("UPDATE agents SET status = :status WHERE id = :id"),
                    {"status": AgentStatus.ERROR, "id": agent_db_id}
                )
                await db.commit()
            except Exception as db_error:
                api_logger.error(f"Failed to update agent status to error: {str(db_error)}")

        # Only process once, so break after first iteration
        break
