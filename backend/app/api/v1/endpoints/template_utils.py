"""
Template utility functions shared across template endpoints.
"""

import json
from typing import Dict, Any


def parse_template_json_fields(template_dict: dict) -> dict:
    """Parse JSON fields in template dictionary to ensure proper types and formats."""
    
    # Parse team_structure_template if it's a string
    if isinstance(template_dict.get("team_structure_template"), str):
        try:
            template_dict["team_structure_template"] = json.loads(template_dict["team_structure_template"])
        except (json.JSONDecodeError, TypeError):
            template_dict["team_structure_template"] = {}
    elif template_dict.get("team_structure_template") is None:
        template_dict["team_structure_template"] = {}

    # Parse default_config if it's a string
    if isinstance(template_dict.get("default_config"), str):
        try:
            template_dict["default_config"] = json.loads(template_dict["default_config"])
        except (json.JSONDecodeError, TypeError):
            template_dict["default_config"] = {}
    elif template_dict.get("default_config") is None:
        template_dict["default_config"] = {}

    # Parse template_metadata if it's a string
    if isinstance(template_dict.get("template_metadata"), str):
        try:
            template_dict["template_metadata"] = json.loads(template_dict["template_metadata"])
        except (json.JSONDecodeError, TypeError):
            template_dict["template_metadata"] = {}
    elif template_dict.get("template_metadata") is None:
        template_dict["template_metadata"] = {}

    # Parse tags if it's a string
    if isinstance(template_dict.get("tags"), str):
        try:
            template_dict["tags"] = json.loads(template_dict["tags"])
        except (json.JSONDecodeError, TypeError):
            template_dict["tags"] = []
    elif template_dict.get("tags") is None:
        template_dict["tags"] = []

    # Parse keywords if it's a string
    if isinstance(template_dict.get("keywords"), str):
        try:
            template_dict["keywords"] = json.loads(template_dict["keywords"])
        except (json.JSONDecodeError, TypeError):
            template_dict["keywords"] = []
    elif template_dict.get("keywords") is None:
        template_dict["keywords"] = []

    # Convert enum values to lowercase for Pydantic compatibility
    enum_fields = ["category", "difficulty", "visibility", "status"]
    for field in enum_fields:
        if template_dict.get(field) and isinstance(template_dict[field], str):
            template_dict[field] = template_dict[field].lower()

    return template_dict


def build_template_response_dict(template: Any, current_user_id: int) -> Dict[str, Any]:
    """Build a template response dictionary from a database row or SQLModel object."""
    # Handle both database row objects (with _fields) and SQLModel objects
    if hasattr(template, '_fields'):
        # Database row object
        template_dict = {column: getattr(template, column) for column in template._fields}
    else:
        # SQLModel object - convert to dict
        template_dict = template.model_dump() if hasattr(template, 'model_dump') else template.dict()

    # Parse JSON fields and normalize enum values
    template_dict = parse_template_json_fields(template_dict)

    # Add ownership and edit permissions
    template_dict["is_owner"] = template_dict["user_id"] == current_user_id
    template_dict["can_edit"] = template_dict["user_id"] == current_user_id

    return template_dict


def build_template_list_response_dict(template: Any, current_user_id: int) -> Dict[str, Any]:
    """Build a template list response dictionary from a database row."""
    # Use only the fields needed for list responses
    list_fields = [
        "id", "template_id", "name", "description", "category", "difficulty",
        "visibility", "status", "tags", "usage_count", "rating", "rating_count",
        "version", "author_name", "use_case", "created_at", "updated_at", "user_id"
    ]
    
    template_dict = {field: getattr(template, field) for field in list_fields if hasattr(template, field)}
    
    # Parse JSON fields and normalize enum values
    template_dict = parse_template_json_fields(template_dict)
    
    # Add ownership and edit permissions
    template_dict["is_owner"] = template_dict["user_id"] == current_user_id
    template_dict["can_edit"] = template_dict["user_id"] == current_user_id
    
    return template_dict
