"""
Template listing and filtering endpoints.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.logging import api_logger
from app.models.planning import (
    TemplateListResponse, PaginationMeta, PaginatedTemplateListResponse,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User
from .template_utils import parse_template_json_fields, build_template_list_response_dict

router = APIRouter()


@router.get("/", response_model=PaginatedTemplateListResponse)
async def list_templates(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    visibility: Optional[TemplateVisibility] = Query(None),
    status: Optional[TemplateStatus] = Query(None),
    search: Optional[str] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    my_templates: bool = Query(False, description="Show only user's templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> PaginatedTemplateListResponse:
    """List templates with filtering and pagination."""
    try:
        # Calculate skip from page
        skip = (page - 1) * limit

        # Build base query
        query_conditions = []
        params = {}

        # User isolation - show public templates and user's own templates
        if my_templates:
            query_conditions.append("user_id = :user_id")
            params["user_id"] = current_user.id
        else:
            query_conditions.append(
                "(visibility IN ('public', 'featured') OR user_id = :user_id)"
            )
            params["user_id"] = current_user.id

        # Status filter - exclude deleted by default
        if status:
            query_conditions.append("status = :status")
            params["status"] = status
        else:
            query_conditions.append("status != 'archived'")

        # Category filter
        if category:
            query_conditions.append("category = :category")
            params["category"] = category

        # Difficulty filter
        if difficulty:
            query_conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Visibility filter
        if visibility:
            query_conditions.append("visibility = :visibility")
            params["visibility"] = visibility

        # Search filter
        if search:
            search_term = f"%{search}%"
            query_conditions.append(
                "(name ILIKE :search OR description ILIKE :search OR use_case ILIKE :search)"
            )
            params["search"] = search_term

        # Tags filter
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            # Use JSON contains operator for tag search
            for i, tag in enumerate(tag_list):
                query_conditions.append(f"JSON_CONTAINS(tags, :tag_{i})")
                params[f"tag_{i}"] = f'"{tag}"'

        # Build where clause
        where_clause = " AND ".join(query_conditions)

        # First, get total count
        count_query = f"""
            SELECT COUNT(*) as total
            FROM templates
            WHERE {where_clause}
        """

        count_result = await db.execute(text(count_query), params)
        total = count_result.scalar()

        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit  # Ceiling division
        has_next = page < total_pages
        has_prev = page > 1

        # Build data query
        data_query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY
                CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
                rating DESC NULLS LAST,
                usage_count DESC,
                created_at DESC
            LIMIT :limit OFFSET :skip
        """

        params.update({"limit": limit, "skip": skip})

        result = await db.execute(text(data_query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = build_template_list_response_dict(template, current_user.id)
            template_responses.append(TemplateListResponse(**template_dict))

        # Create pagination metadata
        pagination = PaginationMeta(
            page=page,
            limit=limit,
            total=total,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )

        return PaginatedTemplateListResponse(
            items=template_responses,
            pagination=pagination
        )

    except Exception as e:
        api_logger.error(f"Failed to list templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list templates: {str(e)}",
        )


@router.get("/legacy", response_model=List[TemplateListResponse])
async def list_templates_legacy(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    visibility: Optional[TemplateVisibility] = Query(None),
    status: Optional[TemplateStatus] = Query(None),
    search: Optional[str] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    my_templates: bool = Query(False, description="Show only user's templates"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Legacy endpoint for backward compatibility - returns templates without pagination metadata."""
    # Convert skip/limit to page-based parameters
    page = (skip // limit) + 1 if limit > 0 else 1

    # Call the main paginated endpoint
    paginated_response = await list_templates(
        page=page,
        limit=limit,
        category=category,
        difficulty=difficulty,
        visibility=visibility,
        status=status,
        search=search,
        tags=tags,
        my_templates=my_templates,
        db=db,
        current_user=current_user
    )

    # Return just the items for backward compatibility
    return paginated_response.items


@router.get("/search", response_model=List[TemplateListResponse])
async def search_templates(
    q: str = Query(..., min_length=1, description="Search query"),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Advanced template search with full-text search capabilities."""
    try:
        # Build search conditions
        search_conditions = []
        params = {"user_id": current_user.id, "limit": limit}

        # Base visibility filter
        search_conditions.append("(visibility IN ('public', 'featured') OR user_id = :user_id)")
        search_conditions.append("status = 'active'")

        # Full-text search across multiple fields
        search_term = f"%{q}%"
        search_conditions.append("""
            (name ILIKE :search
             OR description ILIKE :search
             OR use_case ILIKE :search
             OR author_name ILIKE :search
             OR EXISTS (
                 SELECT 1 FROM json_array_elements_text(tags) AS tag
                 WHERE tag ILIKE :search
             )
             OR EXISTS (
                 SELECT 1 FROM json_array_elements_text(keywords) AS keyword
                 WHERE keyword ILIKE :search
             ))
        """)
        params["search"] = search_term

        # Category filter
        if category:
            search_conditions.append("category = :category")
            params["category"] = category

        # Difficulty filter
        if difficulty:
            search_conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Tags filter
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            for i, tag in enumerate(tag_list):
                search_conditions.append(f"JSON_CONTAINS(tags, :tag_{i})")
                params[f"tag_{i}"] = f'"{tag}"'

        # Build and execute query
        where_clause = " AND ".join(search_conditions)
        query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY
                -- Exact name matches first
                CASE WHEN name ILIKE :exact_search THEN 0 ELSE 1 END,
                -- Featured templates next
                CASE WHEN visibility = 'featured' THEN 0 ELSE 1 END,
                -- Then by relevance score (rating * usage)
                (COALESCE(rating, 0) * COALESCE(usage_count, 0)) DESC,
                created_at DESC
            LIMIT :limit
        """

        params["exact_search"] = f"{q}"

        result = await db.execute(text(query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = build_template_list_response_dict(template, current_user.id)
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to search templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search templates: {str(e)}",
        )
