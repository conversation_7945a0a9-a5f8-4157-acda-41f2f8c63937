"""
User login and authentication endpoints.
"""

from typing import Dict, Union
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.rate_limit import RateLimit
from app.services.logging_service import log_authentication
from app.models.application_log import EventType
from app.models.user import (
    UserLogin, LoginWith2FA, LoginResponse, LoginStepResponse, 
    UserResponse, AuthTokens
)
from app.services.user_service import UserService
from app.core.logging import api_logger
from app.core.config import settings

router = APIRouter()


async def get_request_info(request: Request) -> Dict[str, str]:
    """Extract request information for logging and security."""
    return {
        "ip_address": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "device_info": request.headers.get("user-agent", "unknown"),
        "location": "unknown",  # Could be enhanced with IP geolocation
    }


@router.post("/login", response_model=Union[LoginResponse, LoginStepResponse])
async def login_user(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Union[LoginResponse, LoginStepResponse]:
    """Authenticate user and create session."""
    try:
        # Get request information
        request_info = await get_request_info(request)
        
        # Create user service
        user_service = UserService(db)
        
        # First, verify email and password only
        user = await user_service.verify_credentials(login_data.email, login_data.password)
        
        if not user:
            api_logger.warning(
                "Login failed - invalid credentials",
                email=login_data.email,
                ip_address=request_info.get("ip_address")
            )
            
            # Log failed authentication
            await log_authentication(
                event_type=EventType.USER_LOGIN,
                user_id=None,
                success=False,
                message="Invalid credentials",
                ip_address=request_info.get("ip_address"),
                user_agent=request_info.get("user_agent"),
                metadata={
                    "email": login_data.email,
                    "failure_reason": "Invalid credentials",
                    "login_method": "email_password",
                    "remember_me": login_data.remember_me,
                    "device_info": request_info.get("device_info"),
                    "location": request_info.get("location"),
                }
            )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Check if user has 2FA enabled
        if user.is_2fa_enabled:
            # Store temporary session for 2FA verification
            temp_session_id = await user_service.create_temp_2fa_session(user.id, request_info)

            api_logger.info(
                "Login requires 2FA verification",
                user_id=user.id,
                email=user.email,
                ip_address=request_info.get("ip_address")
            )

            return LoginStepResponse(
                requires_2fa=True,
                user_id=temp_session_id,  # Temporary session ID for 2FA flow
                message="Two-factor authentication required"
            )

        # Complete login for users without 2FA
        access_token = await user_service.complete_login(user, request_info, login_data.remember_me)
        
        # Get session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""
        
        api_logger.info(
            "User logged in successfully",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )
        
        # Log successful authentication
        await log_authentication(
            event_type=EventType.USER_LOGIN,
            user_id=user.id,
            success=True,
            message="User login successful",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            metadata={
                "email": user.email,
                "login_method": "email_password",
                "remember_me": login_data.remember_me,
                "session_id": session_id,
                "device_info": request_info.get("device_info"),
                "location": request_info.get("location"),
            }
        )
        
        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if login_data.remember_me else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Login failed - server error",
            email=login_data.email,
            error=str(e),
            ip_address=request_info.get("ip_address")
        )
        
        # Log failed authentication
        await log_authentication(
            event_type=EventType.USER_LOGIN,
            user_id=None,
            success=False,
            message="Server error during login",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            metadata={
                "email": login_data.email,
                "failure_reason": "Server error",
                "login_method": "email_password",
                "error_type": "server_error",
                "device_info": request_info.get("device_info"),
                "location": request_info.get("location"),
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed. Please try again later."
        )


@router.post("/login-2fa", response_model=LoginResponse)
async def login_user_with_2fa(
    login_data: LoginWith2FA,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Authenticate user with 2FA support."""
    try:
        # Get request information
        request_info = await get_request_info(request)

        # Create user service
        user_service = UserService(db)

        # Authenticate user with 2FA
        user, access_token = await user_service.authenticate_user_with_2fa(login_data, request_info)

        if not user or not access_token:
            api_logger.warning(
                "2FA login failed",
                email=login_data.email,
                ip_address=request_info.get("ip_address")
            )
            
            # Log failed authentication
            await log_authentication(
                event_type=EventType.USER_LOGIN_2FA,
                user_id=None,
                success=False,
                message="Invalid credentials or 2FA code",
                ip_address=request_info.get("ip_address"),
                user_agent=request_info.get("user_agent"),
                metadata={
                    "email": login_data.email,
                    "failure_reason": "Invalid credentials or 2FA code",
                    "login_method": "email_password_2fa",
                    "remember_me": login_data.remember_me,
                    "device_info": request_info.get("device_info"),
                    "location": request_info.get("location"),
                }
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials or verification code"
            )

        # Get session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""

        api_logger.info(
            "User logged in successfully with 2FA",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )
        
        # Log successful authentication
        await log_authentication(
            event_type=EventType.USER_LOGIN_2FA,
            user_id=user.id,
            success=True,
            message="User 2FA login successful",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            metadata={
                "email": user.email,
                "login_method": "email_password_2fa",
                "remember_me": login_data.remember_me,
                "session_id": session_id,
                "device_info": request_info.get("device_info"),
                "location": request_info.get("location"),
            }
        )

        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if login_data.remember_me else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "2FA login failed - server error",
            email=login_data.email,
            error=str(e),
            ip_address=request_info.get("ip_address")
        )
        
        # Log failed authentication
        await log_authentication(
            event_type=EventType.USER_LOGIN_2FA,
            user_id=None,
            success=False,
            message="Server error during 2FA login",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            metadata={
                "email": login_data.email,
                "failure_reason": "Server error",
                "login_method": "email_password_2fa",
                "error_type": "server_error",
                "device_info": request_info.get("device_info"),
                "location": request_info.get("location"),
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed. Please try again later."
        )
