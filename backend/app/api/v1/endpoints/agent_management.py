"""
Agent management endpoints (favorites, status, metrics).
"""

from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError
from app.core.logging import api_logger
from app.core.timezone_utils import utc_now
from app.services.logging_service import log_agent_operation
from app.models.agent import AgentStatus
from app.models.user import User
from app.models.favorites import (
    UserAgentFavorite, UserAgentFavoriteCreate,
    FavoriteAgentResponse, ToggleFavoriteResponse
)

router = APIRouter()


@router.get("/favorites", response_model=List[FavoriteAgentResponse])
async def get_favorite_agents(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[FavoriteAgentResponse]:
    """Get user's favorite agents."""
    try:
        query = """
            SELECT a.*, f.created_at as favorited_at
            FROM agents a
            INNER JOIN user_agent_favorites f ON a.agent_id = f.agent_id
            WHERE f.user_id = :user_id AND a.status != :deleted_status
            ORDER BY f.created_at DESC
        """
        
        result = await db.execute(
            text(query),
            {"user_id": current_user.id, "deleted_status": AgentStatus.DELETED}
        )
        favorites = result.fetchall()

        # Convert to response models
        favorite_responses = []
        for favorite in favorites:
            # Convert Row to dict
            favorite_dict = {column: getattr(favorite, column) for column in favorite._fields}
            
            # Parse team_plan if it's a JSON string
            team_plan = favorite_dict.get("team_plan", {})
            if isinstance(team_plan, str):
                try:
                    import json
                    team_plan = json.loads(team_plan)
                except (json.JSONDecodeError, TypeError):
                    team_plan = {}
            elif team_plan is None:
                team_plan = {}

            # Parse team_members if it's a JSON string
            team_members = favorite_dict.get("team_members", [])
            if isinstance(team_members, str):
                try:
                    import json
                    team_members = json.loads(team_members)
                except (json.JSONDecodeError, TypeError):
                    team_members = []
            elif team_members is None:
                team_members = []

            favorite_response = FavoriteAgentResponse(
                **favorite_dict,
                team_plan=team_plan,
                team_members=team_members,
                favorited_at=favorite_dict["favorited_at"]
            )
            favorite_responses.append(favorite_response)

        api_logger.info(
            "Favorite agents retrieved",
            user_id=current_user.id,
            count=len(favorite_responses),
        )

        return favorite_responses

    except Exception as e:
        api_logger.error(f"Failed to get favorite agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get favorite agents: {str(e)}"
        )


@router.post("/{agent_id}/favorite", response_model=ToggleFavoriteResponse)
async def toggle_agent_favorite(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> ToggleFavoriteResponse:
    """Toggle agent favorite status."""
    try:
        # Check if agent exists and belongs to user
        agent_result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = agent_result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent '{agent_id}' not found"
            )

        # Check if already favorited
        favorite_result = await db.execute(
            text("SELECT * FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
            {"user_id": current_user.id, "agent_id": agent_id}
        )
        existing_favorite = favorite_result.fetchone()

        if existing_favorite:
            # Remove from favorites
            await db.execute(
                text("DELETE FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
                {"user_id": current_user.id, "agent_id": agent_id}
            )
            await db.commit()

            api_logger.info(
                "Agent removed from favorites",
                agent_id=agent_id,
                user_id=current_user.id,
            )

            return ToggleFavoriteResponse(
                agent_id=agent_id,
                is_favorite=False,
                message="Agent removed from favorites"
            )
        else:
            # Add to favorites
            favorite = UserAgentFavorite(
                user_id=current_user.id,
                agent_id=agent_id
            )
            db.add(favorite)
            await db.commit()

            api_logger.info(
                "Agent added to favorites",
                agent_id=agent_id,
                user_id=current_user.id,
            )

            return ToggleFavoriteResponse(
                agent_id=agent_id,
                is_favorite=True,
                message="Agent added to favorites"
            )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to toggle agent favorite {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle favorite: {str(e)}"
        )


@router.get("/{agent_id}/status", response_model=Dict[str, Any])
async def get_agent_status(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get detailed agent status and metrics."""
    try:
        # Get agent basic info
        agent_result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = agent_result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent '{agent_id}' not found"
            )

        # Get agent metrics if available
        metrics_result = await db.execute(
            text("SELECT * FROM agent_metrics WHERE agent_id = :agent_id"),
            {"agent_id": agent_id}
        )
        metrics = metrics_result.fetchone()

        # Check if agent is favorited
        favorite_result = await db.execute(
            text("SELECT * FROM user_agent_favorites WHERE user_id = :user_id AND agent_id = :agent_id"),
            {"user_id": current_user.id, "agent_id": agent_id}
        )
        is_favorite = favorite_result.fetchone() is not None

        # Get recent execution history count
        history_result = await db.execute(
            text("""
                SELECT COUNT(*) as execution_count
                FROM test_history 
                WHERE agent_id = :agent_id AND user_id = :user_id
                AND created_at >= datetime('now', '-30 days')
            """),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        recent_executions = history_result.scalar() or 0

        # Build status response
        status_response = {
            "agent_id": agent_id,
            "team_name": agent.team_name,
            "status": agent.status.value,
            "created_at": agent.created_at.isoformat() if agent.created_at else None,
            "updated_at": agent.updated_at.isoformat() if agent.updated_at else None,
            "last_used": agent.last_used.isoformat() if agent.last_used else None,
            "usage_count": agent.usage_count,
            "is_favorite": is_favorite,
            "recent_executions": recent_executions,
        }

        # Add metrics if available
        if metrics:
            status_response["metrics"] = {
                "success_rate": metrics.success_rate,
                "avg_response_time": metrics.avg_response_time,
                "total_executions": metrics.total_executions,
                "last_execution": metrics.last_execution.isoformat() if metrics.last_execution else None,
            }

        # Add runtime status from dynamic loader
        try:
            from app.services.dynamic_loader import get_dynamic_loader
            dynamic_loader = get_dynamic_loader()
            agent_info = dynamic_loader.get_agent_info(agent_id)
            
            if agent_info:
                status_response["runtime_status"] = {
                    "is_loaded": agent_info.get("is_loaded", False),
                    "runtime_type": agent_info.get("runtime_info", {}).get("type", "unknown"),
                    "supports_progress": agent_info.get("runtime_info", {}).get("supports_progress_callback", False),
                    "supports_variables": agent_info.get("runtime_info", {}).get("supports_variable_tracking", False),
                }
        except Exception as e:
            api_logger.warning(f"Failed to get runtime status for agent {agent_id}: {str(e)}")
            status_response["runtime_status"] = {
                "is_loaded": False,
                "runtime_type": "unknown",
                "supports_progress": False,
                "supports_variables": False,
            }

        api_logger.info(
            "Agent status retrieved",
            agent_id=agent_id,
            user_id=current_user.id,
            status=agent.status.value,
        )

        return status_response

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get agent status {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent status: {str(e)}"
        )


@router.post("/{agent_id}/reload", response_model=Dict[str, Any])
async def reload_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Reload agent in the dynamic loader."""
    try:
        # Check if agent exists and belongs to user
        agent_result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = agent_result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent '{agent_id}' not found"
            )

        # Reload agent using dynamic loader
        from app.services.dynamic_loader import get_dynamic_loader
        dynamic_loader = get_dynamic_loader()
        
        success = dynamic_loader.reload_agent(agent_id)

        if success:
            api_logger.info(
                "Agent reloaded successfully",
                agent_id=agent_id,
                user_id=current_user.id,
            )

            # Log reload event
            await log_agent_operation(
                event_type="agent_reload",
                agent_id=agent_id,
                user_id=current_user.id,
                message=f"Agent reloaded: {agent.team_name}",
                metadata={
                    "team_name": agent.team_name,
                }
            )

            return {
                "agent_id": agent_id,
                "status": "success",
                "message": "Agent reloaded successfully",
                "reloaded_at": utc_now().isoformat(),
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reload agent"
            )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to reload agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reload agent: {str(e)}"
        )
