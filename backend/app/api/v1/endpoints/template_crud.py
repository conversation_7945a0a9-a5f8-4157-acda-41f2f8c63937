"""
Template CRUD operations endpoints.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError, ValidationError
from app.core.logging import api_logger
from app.models.planning import (
    Template, TemplateCreate, TemplateUpdate, TemplateResponse,
    TemplateListResponse, TemplateFromAgentRequest, TemplateStatus
)
from app.models.user import User
from app.models.agent import Agent
from .template_utils import parse_template_json_fields, build_template_response_dict

router = APIRouter()


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Get template by ID."""
    try:
        # Check if template exists and user has access
        query = """
            SELECT * FROM templates 
            WHERE template_id = :template_id 
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(query), 
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Build response dictionary
        template_dict = build_template_response_dict(template, current_user.id)
        
        return TemplateResponse(**template_dict)
        
    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template: {str(e)}",
        )


@router.post("/", response_model=TemplateResponse)
async def create_template(
    template_data: TemplateCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Create a new template."""
    try:
        # Generate unique template ID
        template_id = f"template_{uuid.uuid4().hex[:12]}"
        
        # Create template instance
        template = Template(
            template_id=template_id,
            name=template_data.name,
            description=template_data.description,
            category=template_data.category,
            difficulty=template_data.difficulty,
            visibility=template_data.visibility,
            status=template_data.status,
            tags=template_data.tags,
            keywords=template_data.keywords,
            team_structure_template=template_data.team_structure_template,
            default_config=template_data.default_config,
            template_metadata=template_data.template_metadata,
            version=template_data.version or "1.0.0",
            author_name=template_data.author_name or current_user.username,
            use_case=template_data.use_case,
            user_id=current_user.id,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)
        
        api_logger.info(
            "Template created",
            template_id=template_id,
            name=template.name,
            user_id=current_user.id,
        )
        
        # Build response dictionary
        template_dict = build_template_response_dict(template, current_user.id)

        return TemplateResponse(**template_dict)
        
    except Exception as e:
        api_logger.error(f"Failed to create template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template: {str(e)}",
        )


@router.put("/{template_id}", response_model=TemplateResponse)
@router.patch("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template_update: TemplateUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Update template."""
    try:
        # Check if template exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Build update query dynamically based on provided fields
        update_fields = []
        params = {"template_id": template_id, "user_id": current_user.id}

        # Handle each field that can be updated
        update_data = template_update.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if field in ["team_structure_template", "default_config", "template_metadata", "tags", "keywords"]:
                # JSON fields need special handling
                import json
                update_fields.append(f"{field} = :{field}")
                params[field] = json.dumps(value) if value is not None else None
            else:
                update_fields.append(f"{field} = :{field}")
                params[field] = value

        if not update_fields:
            # No fields to update, return current template
            template_dict = build_template_response_dict(template, current_user.id)
            return TemplateResponse(**template_dict)

        # Add updated_at
        update_fields.append("updated_at = :updated_at")
        params["updated_at"] = datetime.now(timezone.utc).replace(tzinfo=None)

        # Execute update
        update_query = f"""
            UPDATE templates
            SET {', '.join(update_fields)}
            WHERE template_id = :template_id AND user_id = :user_id
        """
        
        await db.execute(text(update_query), params)
        await db.commit()

        # Fetch updated template
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id"),
            {"template_id": template_id}
        )
        updated_template = result.fetchone()

        api_logger.info(
            "Template updated",
            template_id=template_id,
            user_id=current_user.id,
            updated_fields=list(update_data.keys()),
        )

        # Build response dictionary
        template_dict = build_template_response_dict(updated_template, current_user.id)
        
        return TemplateResponse(**template_dict)
        
    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update template: {str(e)}",
        )


@router.delete("/{template_id}")
async def delete_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, str]:
    """Delete template."""
    try:
        # Check if template exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        template = result.fetchone()

        if not template:
            raise NotFoundError("Template", template_id)

        # Delete the template
        await db.execute(
            text("DELETE FROM templates WHERE template_id = :template_id AND user_id = :user_id"),
            {"template_id": template_id, "user_id": current_user.id}
        )
        await db.commit()

        api_logger.info(
            "Template deleted",
            template_id=template_id,
            user_id=current_user.id,
        )

        return {"message": "Template deleted successfully"}
        
    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete template: {str(e)}",
        )
