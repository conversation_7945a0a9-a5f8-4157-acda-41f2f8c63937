"""
Template management endpoints - Main router.
"""

from fastapi import APIRouter

from .template_crud import router as crud_router
from .template_listing import router as listing_router
from .template_validation import router as validation_router
from .template_versioning import router as versioning_router
from .template_deployment import router as deployment_router
from .template_public import router as public_router
from .template_community import router as community_router

router = APIRouter()

# Include all sub-routers
router.include_router(crud_router, tags=["Templates - CRUD"])
router.include_router(listing_router, tags=["Templates - Listing"])
router.include_router(validation_router, tags=["Templates - Validation"])
router.include_router(versioning_router, tags=["Templates - Versioning"])
router.include_router(deployment_router, tags=["Templates - Deployment"])
router.include_router(public_router, tags=["Templates - Public"])
router.include_router(community_router, tags=["Templates - Community"])
