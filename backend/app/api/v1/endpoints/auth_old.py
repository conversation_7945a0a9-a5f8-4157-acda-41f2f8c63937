"""
Authentication endpoints.
"""

from typing import Dict, List, Optional
import os
import uuid
from pathlib import Path

from fastapi import APIRouter, Depends, File, HTTPException, Request, UploadFile, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.auth import (
    CurrentUser, CurrentUserRequired, CurrentActiveUser,
    CurrentVerifiedUser, RequestInfo, get_request_info
)
from app.api.dependencies.database import get_db
from app.api.dependencies.rate_limit import RateLimit
from app.api.dependencies.two_factor import RequireTwoFactorForPasswordChange
from app.services.logging_service import log_authentication
from app.models.user import (
    User, UserRegister, UserLogin, LoginWith2FA, UserUpdate, ChangePassword,
    ResetPassword, ResetPasswordConfirm, UserResponse, UserProfile,
    LoginResponse, LoginStepResponse, AuthTokens, SessionResponse, LoginHistoryResponse
)
from app.services.user_service import UserService
from app.core.logging import api_logger
from app.core.config import settings

router = APIRouter()


@router.post("/register", response_model=LoginResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Register a new user account."""
    try:
        # Get request information
        request_info = await get_request_info(request)
        
        # Create user service
        user_service = UserService(db)
        
        # Create user
        user, access_token = await user_service.create_user(user_data, request_info)
        
        # Create session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""
        
        api_logger.info(
            "User registered successfully",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )
        
        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60,  # 30 days in seconds
            ),
            session_id=session_id,
        )
        
    except ValueError as e:
        api_logger.warning(
            "User registration failed",
            error=str(e),
            email=user_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(
            "User registration error",
            error=str(e),
            email=user_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


from typing import Union

@router.post("/login", response_model=Union[LoginResponse, LoginStepResponse])
async def login_user(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Union[LoginResponse, LoginStepResponse]:
    """Authenticate user and create session."""
    try:
        # Get request information
        request_info = await get_request_info(request)
        
        # Create user service
        user_service = UserService(db)
        
        # First, verify email and password only
        user = await user_service.verify_credentials(login_data.email, login_data.password)

        if not user:
            api_logger.warning(
                "Login failed - invalid credentials",
                email=login_data.email,
                ip_address=request_info.get("ip_address")
            )

            # Log authentication failure
            await log_authentication(
                event_type="user_login",
                user_id=None,
                success=False,
                message=f"Login failed for email: {login_data.email}",
                ip_address=request_info.get("ip_address"),
                user_agent=request_info.get("user_agent"),
                metadata={"email": login_data.email, "reason": "invalid_credentials"}
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Check if user has 2FA enabled
        if user.is_2fa_enabled:
            # Store temporary session for 2FA verification
            temp_session_id = await user_service.create_temp_2fa_session(user.id, request_info)

            api_logger.info(
                "Login requires 2FA verification",
                user_id=user.id,
                email=user.email,
                ip_address=request_info.get("ip_address")
            )

            return LoginStepResponse(
                requires_2fa=True,
                user_id=temp_session_id,  # Temporary session ID for 2FA flow
                message="Two-factor authentication required"
            )

        # Complete login for users without 2FA
        access_token = await user_service.complete_login(user, request_info, login_data.remember_me)
        
        # Get session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""
        
        api_logger.info(
            "User logged in successfully",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )

        # Log successful authentication
        await log_authentication(
            event_type="user_login",
            user_id=user.id,
            success=True,
            message=f"User {user.email} logged in successfully",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            session_id=session_id,
            metadata={
                "email": user.email,
                "remember_me": login_data.remember_me,
                "login_count": user.login_count
            }
        )
        
        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if login_data.remember_me else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )
        
    except ValueError as e:
        api_logger.warning(
            "Login failed",
            error=str(e),
            email=login_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like the one from line 133)
        raise
    except Exception as e:
        api_logger.error(
            "Login error",
            error=str(e),
            email=login_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/verify-2fa", response_model=LoginResponse)
async def verify_2fa_login(
    verify_data: dict,  # {"temp_session_id": str, "totp_code": str, "backup_code": str, "remember_me": bool}
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Complete login with 2FA verification."""
    try:
        # Get request information
        request_info = await get_request_info(request)

        # Create user service
        user_service = UserService(db)

        # Verify 2FA and complete login
        user, access_token = await user_service.verify_2fa_and_complete_login(
            temp_session_id=verify_data.get("temp_session_id"),
            totp_code=verify_data.get("totp_code"),
            backup_code=verify_data.get("backup_code"),
            remember_me=verify_data.get("remember_me", False),
            request_info=request_info
        )

        if not user or not access_token:
            api_logger.warning(
                "2FA verification failed",
                temp_session_id=verify_data.get("temp_session_id"),
                ip_address=request_info.get("ip_address")
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid verification code"
            )

        # Get user sessions for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""

        # Log successful authentication
        await log_authentication(
            event_type="user_login_2fa",
            user_id=user.id,
            success=True,
            message=f"User {user.email} logged in successfully with 2FA",
            metadata={
                "ip_address": request_info.get("ip_address"),
                "user_agent": request_info.get("user_agent"),
                "session_id": session_id,
                "has_totp": bool(verify_data.get("totp_code")),
                "has_backup": bool(verify_data.get("backup_code"))
            }
        )

        api_logger.info(
            "User logged in successfully with 2FA",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address"),
            session_id=session_id
        )

        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if verify_data.get("remember_me", False) else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )

    except ValueError as e:
        api_logger.warning(
            "2FA verification failed",
            error=str(e),
            temp_session_id=verify_data.get("temp_session_id"),
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "2FA verification error",
            error=str(e),
            temp_session_id=verify_data.get("temp_session_id"),
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Verification failed"
        )


@router.post("/login-2fa", response_model=LoginResponse)
async def login_user_with_2fa(
    login_data: LoginWith2FA,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Authenticate user with 2FA support."""
    try:
        # Get request information
        request_info = await get_request_info(request)

        # Create user service
        user_service = UserService(db)

        # Authenticate user with 2FA
        user, access_token = await user_service.authenticate_user_with_2fa(login_data, request_info)

        if not user or not access_token:
            api_logger.warning(
                "2FA login failed - invalid credentials",
                email=login_data.email,
                ip_address=request_info.get("ip_address")
            )

            # Log authentication failure
            await log_authentication(
                event_type="user_login_2fa",
                user_id=None,
                success=False,
                message=f"2FA login failed for email: {login_data.email}",
                metadata={
                    "ip_address": request_info.get("ip_address"),
                    "user_agent": request_info.get("user_agent"),
                    "has_totp": bool(login_data.totp_code),
                    "has_backup": bool(login_data.backup_code)
                }
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials or verification code"
            )

        # Get user sessions for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""

        # Log successful authentication
        await log_authentication(
            event_type="user_login_2fa",
            user_id=user.id,
            success=True,
            message=f"User {user.email} logged in successfully with 2FA",
            metadata={
                "ip_address": request_info.get("ip_address"),
                "user_agent": request_info.get("user_agent"),
                "session_id": session_id,
                "has_totp": bool(login_data.totp_code),
                "has_backup": bool(login_data.backup_code)
            }
        )

        api_logger.info(
            "User logged in successfully with 2FA",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address"),
            session_id=session_id
        )

        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if login_data.remember_me else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )

    except ValueError as e:
        api_logger.warning(
            "2FA login failed",
            error=str(e),
            email=login_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "2FA login error",
            error=str(e),
            email=login_data.email,
            ip_address=request_info.get("ip_address") if 'request_info' in locals() else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Logout user and invalidate session."""
    try:
        # Get session token from request
        auth_header = request.headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header"
            )
        
        session_token = auth_header.split(" ")[1]
        
        # Create user service and logout
        user_service = UserService(db)
        success = await user_service.logout_user(session_token)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Logout failed"
            )
        
        api_logger.info(
            "User logged out successfully",
            user_id=current_user.id,
            email=current_user.email
        )

        # Log logout event
        request_info = await get_request_info(request)
        await log_authentication(
            event_type="user_logout",
            user_id=current_user.id,
            success=True,
            message=f"User {current_user.email} logged out successfully",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            metadata={"email": current_user.email}
        )

        return {"message": "Logged out successfully"}
        
    except Exception as e:
        api_logger.error(
            "Logout error",
            error=str(e),
            user_id=current_user.id if current_user else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: User = CurrentUserRequired,
) -> UserProfile:
    """Get current user profile."""
    return UserProfile(
        id=current_user.id,
        uuid=current_user.uuid,
        name=current_user.name,
        email=current_user.email,
        role=current_user.role,
        status=current_user.status,
        avatar=current_user.avatar,
        bio=current_user.bio,
        timezone=current_user.timezone,
        language=current_user.language,
        last_login_at=current_user.last_login_at,
        is_email_verified=current_user.is_email_verified,
        login_count=current_user.login_count,
        is_2fa_enabled=current_user.is_2fa_enabled,
        two_fa_enabled_at=current_user.two_fa_enabled_at,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        preferences=current_user.preferences,
        last_activity_at=current_user.last_activity_at,
        email_verified_at=current_user.email_verified_at,
        password_changed_at=current_user.password_changed_at,
    )


@router.put("/me", response_model=UserProfile)
async def update_user_profile(
    update_data: UserUpdate,
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> UserProfile:
    """Update current user profile."""
    try:
        user_service = UserService(db)

        # Convert to dict and filter None values
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

        updated_user = await user_service.update_user_profile(current_user, update_dict)

        api_logger.info(
            "User profile updated",
            user_id=updated_user.id,
            updated_fields=list(update_dict.keys())
        )

        return UserProfile(
            id=updated_user.id,
            uuid=updated_user.uuid,
            name=updated_user.name,
            email=updated_user.email,
            role=updated_user.role,
            status=updated_user.status,
            avatar=updated_user.avatar,
            bio=updated_user.bio,
            timezone=updated_user.timezone,
            language=updated_user.language,
            last_login_at=updated_user.last_login_at,
            is_email_verified=updated_user.is_email_verified,
            login_count=updated_user.login_count,
            created_at=updated_user.created_at,
            updated_at=updated_user.updated_at,
            preferences=updated_user.preferences,
            last_activity_at=updated_user.last_activity_at,
            email_verified_at=updated_user.email_verified_at,
            password_changed_at=updated_user.password_changed_at,
        )

    except Exception as e:
        api_logger.error(
            "Profile update error",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.put("/change-password")
async def change_user_password(
    password_data: ChangePassword,
    totp_code: Optional[str] = None,
    backup_code: Optional[str] = None,
    current_user: User = Depends(RequireTwoFactorForPasswordChange),
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Change user password."""
    try:
        user_service = UserService(db)
        success = await user_service.change_password(current_user, password_data)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password change failed"
            )

        api_logger.info(
            "Password changed successfully",
            user_id=current_user.id
        )

        return {"message": "Password changed successfully"}

    except ValueError as e:
        api_logger.warning(
            "Password change failed",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(
            "Password change error",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.post("/reset-password")
async def request_password_reset(
    reset_data: ResetPassword,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Request password reset token."""
    try:
        user_service = UserService(db)
        token = await user_service.create_password_reset_token(reset_data.email)

        # Always return success to prevent email enumeration
        # In a real application, you would send an email with the token
        api_logger.info(
            "Password reset requested",
            email=reset_data.email,
            token_created=token is not None
        )

        return {"message": "If the email exists, a password reset link has been sent"}

    except Exception as e:
        api_logger.error(
            "Password reset request error",
            error=str(e),
            email=reset_data.email
        )
        # Still return success to prevent information disclosure
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password/confirm")
async def confirm_password_reset(
    reset_data: ResetPasswordConfirm,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Confirm password reset with token."""
    try:
        # Validate password confirmation
        if reset_data.new_password != reset_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )

        user_service = UserService(db)
        success = await user_service.reset_password_with_token(
            reset_data.token,
            reset_data.new_password
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password reset failed"
            )

        api_logger.info(
            "Password reset completed",
            token=reset_data.token[:8] + "..."  # Log partial token for debugging
        )

        return {"message": "Password reset successfully"}

    except ValueError as e:
        api_logger.warning(
            "Password reset confirmation failed",
            error=str(e),
            token=reset_data.token[:8] + "..." if len(reset_data.token) > 8 else "short_token"
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(
            "Password reset confirmation error",
            error=str(e),
            token=reset_data.token[:8] + "..." if len(reset_data.token) > 8 else "short_token"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.get("/sessions", response_model=List[SessionResponse])
async def get_user_sessions(
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> List[SessionResponse]:
    """Get user's active sessions."""
    try:
        user_service = UserService(db)
        sessions = await user_service.get_user_sessions(current_user.id)

        return [
            SessionResponse(
                id=session.id,
                uuid=session.uuid,
                session_token=session.session_token[:8] + "...",  # Partial token for security
                status=session.status,
                ip_address=session.ip_address,
                user_agent=session.user_agent,
                device_info=session.device_info,
                location=session.location,
                expires_at=session.expires_at,
                last_activity_at=session.last_activity_at,
                is_secure=session.is_secure,
                is_mobile=session.is_mobile,
                created_at=session.created_at,
            )
            for session in sessions
        ]

    except Exception as e:
        api_logger.error(
            "Get sessions error",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.get("/login-history", response_model=List[LoginHistoryResponse])
async def get_login_history(
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
    limit: int = 50,
) -> List[LoginHistoryResponse]:
    """Get user's login history."""
    try:
        user_service = UserService(db)
        history = await user_service.get_user_login_history(current_user.id, limit)

        return [
            LoginHistoryResponse(
                id=record.id,
                uuid=record.uuid,
                ip_address=record.ip_address,
                user_agent=record.user_agent,
                device_info=record.device_info,
                location=record.location,
                success=record.success,
                failure_reason=record.failure_reason,
                is_suspicious=record.is_suspicious,
                risk_score=record.risk_score,
                created_at=record.created_at,
            )
            for record in history
        ]

    except Exception as e:
        api_logger.error(
            "Get login history error",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve login history"
        )


@router.post("/upload-avatar")
async def upload_user_avatar(
    file: UploadFile = File(...),
    current_user: User = CurrentActiveUser,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Upload user avatar."""
    try:
        # Validate file type
        allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."
            )

        # Validate file size (5MB max)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File too large. Maximum size is 5MB."
            )

        # Create upload directory if it doesn't exist
        upload_dir = Path("data/uploads/avatars")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename
        file_extension = Path(file.filename).suffix if file.filename else ".jpg"
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # Save file
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Update user avatar URL
        avatar_url = f"/uploads/avatars/{unique_filename}"
        user_service = UserService(db)
        await user_service.update_user_profile(current_user, {"avatar": avatar_url})

        api_logger.info(
            "Avatar uploaded successfully",
            user_id=current_user.id,
            filename=unique_filename,
            file_size=len(file_content)
        )

        return {"avatar_url": avatar_url}

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Avatar upload error",
            error=str(e),
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Avatar upload failed"
        )
