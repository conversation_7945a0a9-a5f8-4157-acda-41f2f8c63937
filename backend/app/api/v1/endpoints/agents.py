"""
Agent endpoints - Main router aggregating all agent-related functionality.
"""

from fastapi import APIRouter

from .agent_creation import router as creation_router
from .agent_crud import router as crud_router
from .agent_execution import router as execution_router
from .agent_management import router as management_router

router = APIRouter()

# Include all sub-routers
router.include_router(creation_router, tags=["Agents - Creation"])
router.include_router(crud_router, tags=["Agents - CRUD"])
router.include_router(execution_router, tags=["Agents - Execution"])
router.include_router(management_router, tags=["Agents - Management"])
