"""
Template community endpoints.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError
from app.core.logging import api_logger
from app.models.planning import (
    Template, TemplateResponse, TemplateListResponse,
    TemplateCategory, TemplateDifficulty, TemplateVisibility, TemplateStatus
)
from app.models.user import User
from .template_utils import parse_template_json_fields, build_template_response_dict, build_template_list_response_dict

router = APIRouter()


@router.get("/community", response_model=List[TemplateListResponse])
async def get_community_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[TemplateCategory] = Query(None),
    difficulty: Optional[TemplateDifficulty] = Query(None),
    sort_by: str = Query("popular", regex="^(popular|rating|newest|name)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get community (public) templates."""
    try:
        # Build query conditions
        conditions = ["visibility IN ('public', 'featured')", "status = 'active'"]
        params = {"skip": skip, "limit": limit}

        if category:
            conditions.append("category = :category")
            params["category"] = category

        if difficulty:
            conditions.append("difficulty = :difficulty")
            params["difficulty"] = difficulty

        # Build ORDER BY clause based on sort_by
        order_clauses = {
            "popular": "usage_count DESC, rating DESC NULLS LAST",
            "rating": "rating DESC NULLS LAST, usage_count DESC",
            "newest": "created_at DESC",
            "name": "name ASC"
        }
        order_by = order_clauses.get(sort_by, order_clauses["popular"])

        where_clause = " AND ".join(conditions)
        query = f"""
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE {where_clause}
            ORDER BY {order_by}
            LIMIT :limit OFFSET :skip
        """

        result = await db.execute(text(query), params)
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = build_template_list_response_dict(template, current_user.id)
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get community templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get community templates: {str(e)}",
        )


@router.get("/categories/list", response_model=List[Dict[str, str]])
async def get_template_categories() -> List[Dict[str, str]]:
    """Get available template categories."""
    return [
        {"value": category.value, "label": category.value.replace("_", " ").title()}
        for category in TemplateCategory
    ]


@router.get("/difficulties/list", response_model=List[Dict[str, str]])
async def get_template_difficulties() -> List[Dict[str, str]]:
    """Get available template difficulties."""
    return [
        {"value": difficulty.value, "label": difficulty.value.title()}
        for difficulty in TemplateDifficulty
    ]


@router.post("/{template_id}/duplicate", response_model=TemplateResponse)
async def duplicate_template(
    template_id: str,
    name: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> TemplateResponse:
    """Duplicate an existing template."""
    try:
        # Check if template exists and user has access
        query = """
            SELECT * FROM templates
            WHERE template_id = :template_id
            AND (visibility IN ('public', 'featured') OR user_id = :user_id)
        """
        result = await db.execute(
            text(query),
            {"template_id": template_id, "user_id": current_user.id}
        )
        original_template = result.fetchone()

        if not original_template:
            raise NotFoundError("Template", template_id)

        # Generate new template ID
        new_template_id = f"template_{uuid.uuid4().hex[:12]}"

        # Create duplicate template
        original_dict = {column: getattr(original_template, column) for column in original_template._fields}

        # Parse JSON fields from original template
        def parse_json_field(field_value, default):
            if field_value and isinstance(field_value, str):
                try:
                    import json
                    return json.loads(field_value)
                except (json.JSONDecodeError, TypeError):
                    return default
            return field_value or default

        original_metadata = parse_json_field(original_dict.get("template_metadata"), {})

        # Parse JSON fields from original template before creating new one
        parsed_team_structure = parse_json_field(original_dict["team_structure_template"], {})
        parsed_default_config = parse_json_field(original_dict["default_config"], {})
        parsed_tags = parse_json_field(original_dict["tags"], [])
        parsed_keywords = parse_json_field(original_dict["keywords"], [])

        template = Template(
            template_id=new_template_id,
            name=name or f"{original_dict['name']} (Copy)",
            description=original_dict["description"],
            category=original_dict["category"],
            difficulty=original_dict["difficulty"],
            visibility=TemplateVisibility.PRIVATE,  # Always private for duplicates
            status=TemplateStatus.ACTIVE,
            prompt_template=original_dict["prompt_template"],
            team_structure_template=parsed_team_structure,
            default_config=parsed_default_config,
            tags=parsed_tags,
            keywords=parsed_keywords,
            use_case=original_dict["use_case"],
            example_input=original_dict["example_input"],
            expected_output=original_dict["expected_output"],
            parent_template_id=template_id,  # Link to original
            template_metadata={
                **original_metadata,
                "duplicated_from": template_id,
                "duplicated_at": datetime.now(timezone.utc).isoformat(),
            },
            user_id=current_user.id,
            author_name=current_user.username,
            usage_count=0,
            rating_count=0,
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)

        api_logger.info(
            "Template duplicated",
            original_template_id=template_id,
            new_template_id=new_template_id,
            user_id=current_user.id,
        )

        # Build response dictionary
        template_dict = build_template_response_dict(template, current_user.id)

        return TemplateResponse(**template_dict)

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Failed to duplicate template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to duplicate template: {str(e)}",
        )
