"""
Session management endpoints.
"""

from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.models.user import User, SessionResponse, LoginHistoryResponse
from app.services.user_service import UserService
from app.core.logging import api_logger

router = APIRouter()

# Dependency aliases for cleaner code
CurrentUserRequired = Depends(get_current_active_user)


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Logout user and invalidate session."""
    try:
        # Get session token from request
        auth_header = request.headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header"
            )
        
        session_token = auth_header.split(" ")[1]
        
        # Create user service and logout
        user_service = UserService(db)
        success = await user_service.logout_user(session_token)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Logout failed"
            )

        api_logger.info(
            "User logged out successfully",
            user_id=current_user.id,
            email=current_user.email
        )

        return {"message": "Logged out successfully"}

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Logout failed - server error",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed. Please try again later."
        )


@router.get("/sessions", response_model=List[SessionResponse])
async def get_user_sessions(
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> List[SessionResponse]:
    """Get user's active sessions."""
    try:
        user_service = UserService(db)
        sessions = await user_service.get_user_sessions(current_user.id)

        return [
            SessionResponse(
                id=session.id,
                uuid=session.uuid,
                device_info=session.device_info,
                ip_address=session.ip_address,
                location=session.location,
                is_current=session.is_current,
                last_activity=session.last_activity,
                created_at=session.created_at,
            )
            for session in sessions
        ]

    except Exception as e:
        api_logger.error(
            "Failed to get user sessions",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sessions. Please try again later."
        )


@router.delete("/sessions/{session_uuid}")
async def revoke_user_session(
    session_uuid: str,
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Revoke a specific user session."""
    try:
        user_service = UserService(db)
        success = await user_service.revoke_user_session(current_user.id, session_uuid)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found or already revoked"
            )

        api_logger.info(
            "User session revoked",
            user_id=current_user.id,
            session_uuid=session_uuid
        )

        return {"message": "Session revoked successfully"}

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Failed to revoke user session",
            user_id=current_user.id,
            session_uuid=session_uuid,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke session. Please try again later."
        )


@router.delete("/sessions")
async def revoke_all_user_sessions(
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """Revoke all user sessions except current one."""
    try:
        user_service = UserService(db)
        revoked_count = await user_service.revoke_all_user_sessions(current_user.id)

        api_logger.info(
            "All user sessions revoked",
            user_id=current_user.id,
            revoked_count=revoked_count
        )

        return {
            "message": f"Successfully revoked {revoked_count} sessions",
            "revoked_count": revoked_count
        }

    except Exception as e:
        api_logger.error(
            "Failed to revoke all user sessions",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke sessions. Please try again later."
        )


@router.get("/login-history", response_model=List[LoginHistoryResponse])
async def get_login_history(
    current_user: User = CurrentUserRequired,
    db: AsyncSession = Depends(get_db),
    limit: int = 50,
) -> List[LoginHistoryResponse]:
    """Get user's login history."""
    try:
        user_service = UserService(db)
        history = await user_service.get_user_login_history(current_user.id, limit)

        return [
            LoginHistoryResponse(
                id=record.id,
                uuid=record.uuid,
                ip_address=record.ip_address,
                user_agent=record.user_agent,
                device_info=record.device_info,
                location=record.location,
                success=record.success,
                failure_reason=record.failure_reason,
                is_suspicious=record.is_suspicious,
                risk_score=record.risk_score,
                created_at=record.created_at,
            )
            for record in history
        ]

    except Exception as e:
        api_logger.error(
            "Failed to get login history",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get login history. Please try again later."
        )
