"""
Authentication endpoints - Main router aggregating all auth-related functionality.
"""

from fastapi import APIRouter

from .auth_registration import router as registration_router
from .auth_login import router as login_router
from .auth_2fa import router as twofa_router
from .auth_profile import router as profile_router
from .auth_password import router as password_router
from .auth_session import router as session_router

router = APIRouter()

# Include all sub-routers
router.include_router(registration_router, tags=["Auth - Registration"])
router.include_router(login_router, tags=["Auth - Login"])
router.include_router(twofa_router, tags=["Auth - 2FA"])
router.include_router(profile_router, tags=["Auth - Profile"])
router.include_router(password_router, tags=["Auth - Password"])
router.include_router(session_router, tags=["Auth - Session"])
