"""
Public template endpoints (no authentication required).
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.logging import api_logger
from app.models.planning import TemplateListResponse
from app.models.user import User
from .template_utils import parse_template_json_fields, build_template_list_response_dict

router = APIRouter()


@router.get("/public/featured", response_model=List[TemplateListResponse])
async def get_featured_templates_public(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_db),
) -> List[TemplateListResponse]:
    """Get featured templates without authentication (public endpoint)."""
    try:
        # Query featured templates
        query = """
            SELECT * FROM templates
            WHERE visibility = 'featured' AND status = 'active'
            ORDER BY created_at DESC
            LIMIT :limit
        """
        result = await db.execute(text(query), {"limit": limit})
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = {column: getattr(template, column) for column in template._fields}

            # Parse JSON fields and normalize enum values
            template_dict = parse_template_json_fields(template_dict)

            # Public templates - no ownership info
            template_dict["is_owner"] = False
            template_dict["can_edit"] = False
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get featured templates (public): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get featured templates: {str(e)}",
        )


@router.get("/featured", response_model=List[TemplateListResponse])
async def get_featured_templates(
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[TemplateListResponse]:
    """Get featured public templates."""
    try:
        query = """
            SELECT id, template_id, name, description, category, difficulty,
                   visibility, status, tags, usage_count, rating, rating_count,
                   version, author_name, use_case, created_at, updated_at, user_id
            FROM templates
            WHERE visibility = 'featured' AND status = 'active'
            ORDER BY rating DESC NULLS LAST, usage_count DESC, created_at DESC
            LIMIT :limit
        """

        result = await db.execute(text(query), {"limit": limit})
        templates = result.fetchall()

        # Convert to response models
        template_responses = []
        for template in templates:
            template_dict = build_template_list_response_dict(template, current_user.id)
            template_responses.append(TemplateListResponse(**template_dict))

        return template_responses

    except Exception as e:
        api_logger.error(f"Failed to get featured templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get featured templates: {str(e)}",
        )


@router.get("/tags/popular", response_model=List[Dict[str, Any]])
async def get_popular_tags(
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[Dict[str, Any]]:
    """Get popular tags across all public templates."""
    try:
        # Get all templates with tags (SQLite compatible approach)
        query = """
            SELECT tags, rating, usage_count
            FROM templates
            WHERE visibility IN ('public', 'featured')
            AND status = 'active'
            AND tags IS NOT NULL
            AND tags != '[]'
        """

        result = await db.execute(text(query))
        templates = result.fetchall()

        # Process tags in Python since SQLite doesn't have json_array_elements_text
        tag_stats = {}

        for template in templates:
            try:
                # Parse JSON tags
                import json
                tags = json.loads(template.tags) if isinstance(template.tags, str) else template.tags

                if isinstance(tags, list):
                    for tag in tags:
                        if tag not in tag_stats:
                            tag_stats[tag] = {
                                'count': 0,
                                'total_rating': 0,
                                'rating_count': 0,
                                'total_usage': 0
                            }

                        tag_stats[tag]['count'] += 1
                        tag_stats[tag]['total_usage'] += template.usage_count or 0

                        if template.rating:
                            tag_stats[tag]['total_rating'] += template.rating
                            tag_stats[tag]['rating_count'] += 1

            except (json.JSONDecodeError, TypeError):
                continue

        # Convert to result format and sort
        popular_tags = []
        for tag, stats in tag_stats.items():
            if stats['count'] > 1:  # Only tags used more than once
                avg_rating = (stats['total_rating'] / stats['rating_count']) if stats['rating_count'] > 0 else 0
                popular_tags.append({
                    "tag": tag,
                    "count": stats['count'],
                    "avg_rating": round(avg_rating, 2),
                    "total_usage": stats['total_usage']
                })

        # Sort by count desc, then by total usage desc
        popular_tags.sort(key=lambda x: (x['count'], x['total_usage']), reverse=True)

        return popular_tags[:limit]

    except Exception as e:
        api_logger.error(f"Failed to get popular tags: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular tags: {str(e)}",
        )


@router.get("/stats", response_model=Dict[str, Any])
async def get_template_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get template statistics and analytics."""
    try:
        # Get overall stats
        stats_query = """
            SELECT
                COUNT(*) as total_templates,
                COUNT(CASE WHEN visibility = 'public' THEN 1 END) as public_templates,
                COUNT(CASE WHEN visibility = 'featured' THEN 1 END) as featured_templates,
                COUNT(CASE WHEN user_id = :user_id THEN 1 END) as user_templates,
                AVG(rating) as avg_rating,
                SUM(usage_count) as total_usage,
                COUNT(DISTINCT category) as categories_count
            FROM templates
            WHERE status = 'active'
        """

        result = await db.execute(text(stats_query), {"user_id": current_user.id})
        stats = result.fetchone()

        # Get category distribution
        category_query = """
            SELECT
                category,
                COUNT(*) as count,
                AVG(rating) as avg_rating,
                SUM(usage_count) as total_usage
            FROM templates
            WHERE status = 'active'
            AND visibility IN ('public', 'featured')
            GROUP BY category
            ORDER BY count DESC
        """

        result = await db.execute(text(category_query))
        categories = result.fetchall()

        # Get difficulty distribution
        difficulty_query = """
            SELECT
                difficulty,
                COUNT(*) as count,
                AVG(rating) as avg_rating
            FROM templates
            WHERE status = 'active'
            AND visibility IN ('public', 'featured')
            GROUP BY difficulty
            ORDER BY
                CASE difficulty
                    WHEN 'beginner' THEN 1
                    WHEN 'intermediate' THEN 2
                    WHEN 'advanced' THEN 3
                    WHEN 'expert' THEN 4
                END
        """

        result = await db.execute(text(difficulty_query))
        difficulties = result.fetchall()

        return {
            "overview": {
                "total_templates": stats.total_templates,
                "public_templates": stats.public_templates,
                "featured_templates": stats.featured_templates,
                "user_templates": stats.user_templates,
                "avg_rating": round(float(stats.avg_rating or 0), 2),
                "total_usage": stats.total_usage,
                "categories_count": stats.categories_count,
            },
            "categories": [
                {
                    "category": cat.category,
                    "count": cat.count,
                    "avg_rating": round(float(cat.avg_rating or 0), 2),
                    "total_usage": cat.total_usage,
                }
                for cat in categories
            ],
            "difficulties": [
                {
                    "difficulty": diff.difficulty,
                    "count": diff.count,
                    "avg_rating": round(float(diff.avg_rating or 0), 2),
                }
                for diff in difficulties
            ],
        }

    except Exception as e:
        api_logger.error(f"Failed to get template stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get template stats: {str(e)}",
        )
