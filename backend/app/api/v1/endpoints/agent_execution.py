"""
Agent execution endpoints.
"""

import uuid
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError
from app.core.logging import api_logger
from app.core.timezone_utils import utc_now
from app.services.logging_service import log_agent_operation
from app.models.agent import AgentStatus
from app.models.user import User
from app.services.dynamic_loader import get_dynamic_loader
from app.services.variable_discovery import VariableDiscoveryService
from app.services.websocket_service import variable_tracker

router = APIRouter()


@router.post("/{agent_id}/execute", response_model=Dict[str, Any])
async def execute_agent(
    agent_id: str,
    input_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Execute agent with input data and return response."""
    try:
        # Check if agent exists, is active, and belongs to current user
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND status = :status AND user_id = :user_id"),
            {"agent_id": agent_id, "status": AgentStatus.ACTIVE, "user_id": current_user.id},
        )
        agent = result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Active agent '{agent_id}' not found",
            )

        # Validate input
        user_input = input_data.get("input", "")
        if not user_input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Input is required"
            )

        # Generate execution ID for tracking
        execution_id = f"exec_{uuid.uuid4().hex[:12]}"
        test_id = input_data.get("test_id")  # Optional test ID for variable tracking

        api_logger.info(
            "Agent execution started",
            agent_id=agent_id,
            execution_id=execution_id,
            user_id=current_user.id,
            test_id=test_id,
        )

        # Prepare agent configuration from database
        agent_config = {
            "agent_id": agent_id,
            "team_name": agent.team_name,
            "description": agent.description,
            "team_plan": agent.team_plan,
            "team_members": agent.team_members,
            "prompt_template": agent.prompt_template,
            "system_prompt": agent.system_prompt,
            "metadata": {
                "ready_to_deploy": True,
                "requires_ai_generation": False
            }
        }

        # Parse team_plan if it's a JSON string
        if isinstance(agent_config["team_plan"], str):
            try:
                import json
                agent_config["team_plan"] = json.loads(agent_config["team_plan"])
            except (json.JSONDecodeError, TypeError):
                agent_config["team_plan"] = {}

        # Parse team_members if it's a JSON string
        if isinstance(agent_config["team_members"], str):
            try:
                import json
                agent_config["team_members"] = json.loads(agent_config["team_members"])
            except (json.JSONDecodeError, TypeError):
                agent_config["team_members"] = []

        # Prepare execution input
        execution_input = {
            "input": user_input,
            "execution_id": execution_id,
            "user_id": current_user.id,
            "agent_id": agent_id,
        }

        # Add any additional parameters from input_data
        for key, value in input_data.items():
            if key not in ["input", "test_id"]:
                execution_input[key] = value

        # Progress callback for real-time updates
        async def progress_callback(progress_data: Dict[str, Any]):
            """Handle progress updates during agent execution."""
            try:
                # Add execution context
                progress_data.update({
                    "agent_id": agent_id,
                    "execution_id": execution_id,
                    "timestamp": utc_now().isoformat(),
                })

                # Extract and broadcast variables if test_id is provided
                if test_id:
                    await extract_and_broadcast_variables(
                        agent_id=agent_id,
                        progress_data=progress_data,
                        test_id=test_id,
                        db=db
                    )

                api_logger.debug(
                    "Agent execution progress",
                    agent_id=agent_id,
                    execution_id=execution_id,
                    progress=progress_data,
                )

            except Exception as e:
                api_logger.error(f"Progress callback error: {str(e)}")

        # Execute agent using dynamic loader
        dynamic_loader = get_dynamic_loader()
        response = await dynamic_loader.execute_agent(
            agent_id=agent_id,
            input_data=execution_input,
            agent_config=agent_config,
            progress_callback=progress_callback,
            current_user=current_user,
            test_id=test_id
        )

        # Update usage statistics
        await db.execute(
            text("""
                UPDATE agents 
                SET usage_count = usage_count + 1, last_used = :last_used 
                WHERE agent_id = :agent_id
            """),
            {"last_used": utc_now().replace(tzinfo=None), "agent_id": agent_id}
        )
        await db.commit()

        # Log successful execution
        await log_agent_operation(
            event_type="agent_execute",
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent executed successfully: {agent.team_name}",
            metadata={
                "execution_id": execution_id,
                "input_length": len(user_input),
                "output_length": len(str(response.get("output", ""))),
                "status": response.get("status", "unknown"),
                "test_id": test_id,
            }
        )

        api_logger.info(
            "Agent execution completed",
            agent_id=agent_id,
            execution_id=execution_id,
            status=response.get("status", "unknown"),
            user_id=current_user.id,
        )

        # Add execution metadata to response
        response.update({
            "execution_id": execution_id,
            "agent_id": agent_id,
            "executed_at": utc_now().isoformat(),
        })

        return response

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Agent execution failed",
            agent_id=agent_id,
            error=str(e),
            user_id=current_user.id,
        )

        # Log failed execution
        try:
            await log_agent_operation(
                event_type="agent_execute_failed",
                agent_id=agent_id,
                user_id=current_user.id,
                message=f"Agent execution failed: {str(e)}",
                metadata={
                    "error": str(e),
                    "input_data": input_data,
                }
            )
        except Exception as log_error:
            api_logger.error(f"Failed to log execution failure: {str(log_error)}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent execution failed: {str(e)}"
        )


@router.get("/{agent_id}/variables", response_model=Dict[str, Any])
async def discover_agent_variables(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Discover variables that can be extracted from agent execution."""
    try:
        # Check if agent exists and belongs to user
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent '{agent_id}' not found"
            )

        # Parse team_plan if it's a JSON string
        team_plan = agent.team_plan
        if isinstance(team_plan, str):
            try:
                import json
                team_plan = json.loads(team_plan)
            except (json.JSONDecodeError, TypeError):
                team_plan = {}

        # Use variable discovery service
        discovery_service = VariableDiscoveryService()
        variables = discovery_service.discover_team_variables(team_plan)

        api_logger.info(
            "Variables discovered for agent",
            agent_id=agent_id,
            variables_count=len(variables),
            user_id=current_user.id,
        )

        return {
            "agent_id": agent_id,
            "variables": variables,
            "discovered_at": utc_now().isoformat(),
            "total_variables": len(variables),
        }

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to discover variables for agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to discover variables: {str(e)}"
        )


async def extract_and_broadcast_variables(
    agent_id: str, 
    progress_data: Dict[str, Any], 
    test_id: Optional[str] = None, 
    db: Optional[AsyncSession] = None
):
    """
    Extract variable information from progress data and broadcast via WebSocket.

    This function analyzes progress data for variable resolution events and
    broadcasts them to connected WebSocket clients for real-time tracking.
    """
    try:
        if not test_id:
            return

        # Extract variables from progress data
        variables_found = []
        
        # Check if this is a step completion with output
        if progress_data.get("status") == "completed" and "result" in progress_data:
            step_result = progress_data["result"]
            step_name = step_result.get("step_name", "")
            output = step_result.get("output", "")
            
            if output:
                # Use variable discovery service to extract variables
                discovery_service = VariableDiscoveryService()
                extracted_vars = await discovery_service.extract_variables_from_text(
                    text=output,
                    context={
                        "step_name": step_name,
                        "agent_id": agent_id,
                        "execution_id": progress_data.get("execution_id"),
                    }
                )
                
                variables_found.extend(extracted_vars)

        # Broadcast variables if any were found
        if variables_found:
            variable_data = {
                "test_id": test_id,
                "agent_id": agent_id,
                "execution_id": progress_data.get("execution_id"),
                "variables": variables_found,
                "timestamp": utc_now().isoformat(),
                "event_type": "variables_discovered",
            }
            
            # Broadcast via WebSocket
            await variable_tracker.broadcast_variable_update(variable_data)
            
            api_logger.debug(
                "Variables extracted and broadcasted",
                test_id=test_id,
                agent_id=agent_id,
                variables_count=len(variables_found),
            )

    except Exception as e:
        api_logger.error(f"Failed to extract and broadcast variables: {str(e)}")


async def process_agent_execution(
    agent_id: str,
    execution_id: str,
    input_data: Dict[str, Any],
) -> None:
    """Process agent execution in background."""
    # TODO: Implement actual agent execution logic
    api_logger.info(
        "Agent execution completed",
        agent_id=agent_id,
        execution_id=execution_id,
    )
