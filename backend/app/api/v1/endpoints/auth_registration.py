"""
User registration endpoints.
"""

from typing import Dict
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.rate_limit import RateLimit
from app.services.logging_service import log_authentication
from app.models.user import (
    UserRegister, LoginResponse, UserResponse, AuthTokens
)
from app.services.user_service import UserService
from app.core.logging import api_logger
from app.core.config import settings

router = APIRouter()


async def get_request_info(request: Request) -> Dict[str, str]:
    """Extract request information for logging and security."""
    return {
        "ip_address": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "device_info": request.headers.get("user-agent", "unknown"),
        "location": "unknown",  # Could be enhanced with IP geolocation
    }


@router.post("/register", response_model=LoginResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Register a new user account."""
    try:
        # Get request information
        request_info = await get_request_info(request)
        
        # Create user service
        user_service = UserService(db)
        
        # Create user
        user, access_token = await user_service.create_user(user_data, request_info)
        
        # Create session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""
        
        api_logger.info(
            "User registered successfully",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )
        
        # Log authentication event
        await log_authentication(
            event_type="user_register",
            user_id=user.id,
            email=user.email,
            success=True,
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            device_info=request_info.get("device_info"),
            location=request_info.get("location"),
            metadata={
                "registration_method": "email",
                "user_name": user.name,
            }
        )
        
        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60,  # 30 days in seconds
            ),
            session_id=session_id,
        )

    except ValueError as e:
        api_logger.warning(
            "User registration failed - validation error",
            email=user_data.email,
            error=str(e),
            ip_address=request.client.host if request.client else "unknown"
        )
        
        # Log failed authentication
        await log_authentication(
            event_type="user_register",
            email=user_data.email,
            success=False,
            failure_reason=str(e),
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent", "unknown"),
            metadata={
                "registration_method": "email",
                "error_type": "validation_error",
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(
            "User registration failed - server error",
            email=user_data.email,
            error=str(e),
            ip_address=request.client.host if request.client else "unknown"
        )
        
        # Log failed authentication
        await log_authentication(
            event_type="user_register",
            email=user_data.email,
            success=False,
            failure_reason="Server error",
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent", "unknown"),
            metadata={
                "registration_method": "email",
                "error_type": "server_error",
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed. Please try again later."
        )
