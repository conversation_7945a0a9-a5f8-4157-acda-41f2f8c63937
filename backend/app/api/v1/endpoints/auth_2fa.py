"""
Two-factor authentication endpoints.
"""

from typing import Dict
from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.rate_limit import RateLimit
from app.services.logging_service import log_authentication
from app.models.user import LoginResponse, UserResponse, AuthTokens
from app.services.user_service import UserService
from app.core.logging import api_logger
from app.core.config import settings

router = APIRouter()


async def get_request_info(request: Request) -> Dict[str, str]:
    """Extract request information for logging and security."""
    return {
        "ip_address": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "device_info": request.headers.get("user-agent", "unknown"),
        "location": "unknown",  # Could be enhanced with IP geolocation
    }


@router.post("/verify-2fa", response_model=LoginResponse)
async def verify_2fa_login(
    verify_data: dict,  # {"temp_session_id": str, "totp_code": str, "backup_code": str, "remember_me": bool}
    request: Request,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> LoginResponse:
    """Complete login with 2FA verification."""
    try:
        # Get request information
        request_info = await get_request_info(request)

        # Create user service
        user_service = UserService(db)

        # Verify 2FA and complete login
        user, access_token = await user_service.verify_2fa_and_complete_login(
            temp_session_id=verify_data.get("temp_session_id"),
            totp_code=verify_data.get("totp_code"),
            backup_code=verify_data.get("backup_code"),
            remember_me=verify_data.get("remember_me", False),
            request_info=request_info
        )

        if not user or not access_token:
            api_logger.warning(
                "2FA verification failed",
                temp_session_id=verify_data.get("temp_session_id"),
                ip_address=request_info.get("ip_address")
            )

            # Log failed authentication
            await log_authentication(
                event_type="user_2fa_verify",
                temp_session_id=verify_data.get("temp_session_id"),
                success=False,
                failure_reason="Invalid verification code",
                ip_address=request_info.get("ip_address"),
                user_agent=request_info.get("user_agent"),
                device_info=request_info.get("device_info"),
                location=request_info.get("location"),
                metadata={
                    "verification_method": "totp_or_backup",
                    "remember_me": verify_data.get("remember_me", False),
                }
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid verification code"
            )

        # Get session for response
        sessions = await user_service.get_user_sessions(user.id)
        session_id = sessions[0].uuid if sessions else ""

        api_logger.info(
            "2FA verification successful",
            user_id=user.id,
            email=user.email,
            ip_address=request_info.get("ip_address")
        )

        # Log successful authentication
        await log_authentication(
            event_type="user_2fa_verify",
            user_id=user.id,
            email=user.email,
            success=True,
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            device_info=request_info.get("device_info"),
            location=request_info.get("location"),
            metadata={
                "verification_method": "totp_or_backup",
                "remember_me": verify_data.get("remember_me", False),
                "session_id": session_id,
            }
        )

        return LoginResponse(
            user=UserResponse(
                id=user.id,
                uuid=user.uuid,
                name=user.name,
                email=user.email,
                role=user.role,
                status=user.status,
                avatar=user.avatar,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                last_login_at=user.last_login_at,
                is_email_verified=user.is_email_verified,
                login_count=user.login_count,
                is_2fa_enabled=user.is_2fa_enabled,
                two_fa_enabled_at=user.two_fa_enabled_at,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ),
            tokens=AuthTokens(
                access_token=access_token,
                token_type="bearer",
                expires_in=30 * 24 * 60 * 60 if verify_data.get("remember_me", False) else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            ),
            session_id=session_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "2FA verification failed - server error",
            temp_session_id=verify_data.get("temp_session_id"),
            error=str(e),
            ip_address=request_info.get("ip_address")
        )

        # Log failed authentication
        await log_authentication(
            event_type="user_2fa_verify",
            temp_session_id=verify_data.get("temp_session_id"),
            success=False,
            failure_reason="Server error",
            ip_address=request_info.get("ip_address"),
            user_agent=request_info.get("user_agent"),
            device_info=request_info.get("device_info"),
            location=request_info.get("location"),
            metadata={
                "verification_method": "totp_or_backup",
                "error_type": "server_error",
            }
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Verification failed. Please try again later."
        )
