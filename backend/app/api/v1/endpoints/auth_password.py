"""
Password management endpoints.
"""

from typing import Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.api.dependencies.rate_limit import RateLimit
from app.api.dependencies.two_factor import RequireTwoFactorForPasswordChange
from app.models.user import User, ChangePassword, ResetPassword, ResetPasswordConfirm
from app.services.user_service import UserService
from app.core.logging import api_logger

router = APIRouter()


@router.put("/change-password")
async def change_user_password(
    password_data: ChangePassword,
    totp_code: Optional[str] = None,
    backup_code: Optional[str] = None,
    current_user: User = Depends(RequireTwoFactorForPasswordChange),
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Change user password."""
    try:
        user_service = UserService(db)
        success = await user_service.change_password(current_user, password_data)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password change failed"
            )

        api_logger.info(
            "Password changed successfully",
            user_id=current_user.id
        )

        return {"message": "Password changed successfully"}

    except ValueError as e:
        api_logger.warning(
            "Password change failed - validation error",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Password change failed - server error",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed. Please try again later."
        )


@router.post("/reset-password")
async def request_password_reset(
    reset_data: ResetPassword,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Request password reset token."""
    try:
        user_service = UserService(db)
        token = await user_service.create_password_reset_token(reset_data.email)

        # Always return success to prevent email enumeration
        # In a real application, you would send an email with the token
        api_logger.info(
            "Password reset requested",
            email=reset_data.email,
            token_created=token is not None
        )

        return {"message": "If the email exists, a password reset link has been sent"}

    except Exception as e:
        api_logger.error(
            "Password reset request failed",
            email=reset_data.email,
            error=str(e)
        )
        # Still return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password/confirm")
async def confirm_password_reset(
    reset_data: ResetPasswordConfirm,
    db: AsyncSession = Depends(get_db),
    _: None = RateLimit,
) -> Dict[str, str]:
    """Confirm password reset with token."""
    try:
        user_service = UserService(db)
        success = await user_service.reset_password_with_token(
            reset_data.token,
            reset_data.new_password
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )

        api_logger.info(
            "Password reset completed successfully",
            token=reset_data.token[:8] + "..."  # Log only first 8 chars for security
        )

        return {"message": "Password reset successfully"}

    except ValueError as e:
        api_logger.warning(
            "Password reset confirmation failed - validation error",
            token=reset_data.token[:8] + "..." if reset_data.token else "none",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Password reset confirmation failed - server error",
            token=reset_data.token[:8] + "..." if reset_data.token else "none",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed. Please try again later."
        )
