"""
Template validation endpoints.
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import NotFoundError, ValidationError
from app.core.logging import api_logger
from app.models.user import User

router = APIRouter()


@router.post("/{template_id}/validate", response_model=Dict[str, Any])
async def validate_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Validate template completeness and deployability."""
    try:
        from app.services.template_management_service import TemplateManagementService

        template_service = TemplateManagementService(db)
        validation_result = await template_service.validate_template_completeness(
            template_id, current_user
        )

        api_logger.info(
            "Template validation completed",
            template_id=template_id,
            user_id=current_user.id,
            validation_score=validation_result["validation_score"],
            is_deployable=validation_result["is_deployable"],
        )

        return validation_result

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Template '{template_id}' not found"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to validate template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate template: {str(e)}",
        )
